# OSS多语言支持说明

## 概述

OSS上传接口现在支持多语言国际化，可以根据客户端的语言设置返回相应语言的错误信息和提示信息。

## 支持的语言

目前支持以下语言：

| 语言代码 | 语言名称 | 示例 |
|---------|----------|------|
| zh-CN | 简体中文 | 文件过大，最大支持10MB |
| en-US | 英语 | File too large, maximum 10MB supported |
| ja-JP | 日语 | ファイルが大きすぎます。最大10MBまでサポートされています |
| zh-HK | 繁体中文（香港） | 支持但需要添加翻译 |
| ko-KR | 韩语 | 支持但需要添加翻译 |
| fr-FR | 法语 | 支持但需要添加翻译 |
| de-DE | 德语 | 支持但需要添加翻译 |
| 其他 | 更多语言 | 可根据需要添加 |

## 使用方式

### 客户端设置

客户端需要在HTTP请求头中设置 `Accept-Language` 字段：

```javascript
fetch('/api/oss/uploadImg', {
    method: 'POST',
    headers: {
        'Accept-Language': 'en-US',  // 设置语言
        'X-Access-Token': 'your-token'
    },
    body: formData
});
```

### 支持的语言代码

```javascript
// 常用语言代码
const languages = {
    'zh-CN': '简体中文',
    'en-US': 'English',
    'ja-JP': '日本語',
    'zh-HK': '繁體中文（香港）',
    'zh-TW': '繁體中文（台灣）',
    'ko-KR': '한국어',
    'fr-FR': 'Français',
    'de-DE': 'Deutsch',
    'it-IT': 'Italiano',
    'pt-PT': 'Português',
    'es-ES': 'Español',
    'ru-RU': 'Русский',
    'th-TH': 'ไทย',
    'vi-VN': 'Tiếng Việt'
};
```

## 国际化消息

### 基础错误信息

| 中文 | 英文 | 日文 |
|------|------|------|
| 上传图片不能为空 | Upload file cannot be empty | 画像をアップロードしてください |
| 用户ID不能为空 | User ID cannot be empty | ユーザーIDを入力してください |
| 上传失败，请稍后重试 | Upload failed, please try again later | アップロードに失敗しました。後でもう一度お試しください |

### 文件验证错误

| 中文 | 英文 | 日文 |
|------|------|------|
| 文件类型不正确，只支持jpg、jpeg、png、bmp | File type incorrect, only jpg、jpeg、png、bmp supported | ファイルタイプが正しくありません。jpg、jpeg、png、bmpのみサポートされています |
| 文件过大，最大支持{0}MB | File too large, maximum {0}MB supported | ファイルが大きすぎます。最大{0}MBまでサポートされています |
| 文件内容不是有效图片 | File content is not a valid image | ファイル内容が有効な画像ではありません |

### 批量上传信息

| 中文 | 英文 | 日文 |
|------|------|------|
| 所有文件上传成功 | All files uploaded successfully | すべてのファイルのアップロードが成功しました |
| 所有文件上传失败 | All files upload failed | すべてのファイルのアップロードが失敗しました |
| 批量上传最多支持{0}张图片，当前选择了{1}张 | Batch upload supports maximum {0} images, currently selected {1} | バッチアップロードは最大{0}枚の画像をサポートしています。現在{1}枚選択されています |

## 响应示例

### 中文响应
```json
{
  "state": "fail",
  "msg": "文件过大，最大支持10MB"
}
```

### 英文响应
```json
{
  "state": "fail",
  "msg": "File too large, maximum 10MB supported"
}
```

### 日文响应
```json
{
  "state": "fail",
  "msg": "ファイルが大きすぎます。最大10MBまでサポートされています"
}
```

## 参数化消息

某些消息包含动态参数，使用 `{0}`, `{1}` 等占位符：

### 示例：文件大小限制
- **模板**: `文件过大，最大支持{0}MB`
- **实际**: `文件过大，最大支持10MB`

### 示例：批量上传限制
- **模板**: `批量上传最多支持{0}张图片，当前选择了{1}张`
- **实际**: `批量上传最多支持10张图片，当前选择了15张`

## 技术实现

### 1. 国际化拦截器

系统使用 `I18nInterceptor` 和 `AppUserInterceptor` 来处理国际化：

```java
// 从请求头获取语言设置
String locale = con.getHeader("Accept-Language");

// 使用国际化资源
Res resLocale = I18n.use(locale);
String localeMsg = resLocale.get(msg);
```

### 2. 消息Key常量

在 `OssConfig.MessageKeys` 中定义了所有消息的Key：

```java
public static final String FILE_SIZE_EXCEEDED = "文件过大，最大支持{0}MB";
public static final String BATCH_COUNT_EXCEEDED = "批量上传最多支持{0}张图片，当前选择了{1}张";
```

### 3. 资源文件

国际化资源存储在 `src/main/resources/` 目录下：

- `i18n_zh_CN.properties` - 简体中文
- `i18n_en_US.properties` - 英语
- `i18n_ja_JP.properties` - 日语
- 其他语言文件...

## 添加新语言

### 1. 创建资源文件

创建新的属性文件，例如 `i18n_ko_KR.properties`：

```properties
# OSS 업로드 관련 국제화
用户ID不能为空=사용자 ID를 입력해주세요
文件过大，最大支持{0}MB=파일이 너무 큽니다. 최대 {0}MB까지 지원됩니다
# ... 其他翻译
```

### 2. 更新拦截器

确保新语言代码在拦截器的 `LOCALE_MAP` 中：

```java
LOCALE_MAP.put("ko-KR", String.valueOf(Locale.KOREA));
```

### 3. 测试验证

使用新语言代码测试接口：

```bash
curl -H "Accept-Language: ko-KR" \
     -H "X-Access-Token: your-token" \
     -F "file=@test.jpg" \
     http://localhost:8080/api/oss/uploadImg
```

## 最佳实践

### 1. 一致性

- 保持所有语言的消息格式一致
- 使用相同的参数占位符位置
- 保持专业和友好的语调

### 2. 参数处理

- 确保参数在所有语言中都有意义
- 考虑不同语言的语法结构
- 测试参数替换的正确性

### 3. 回退机制

- 如果找不到对应语言的翻译，系统会使用默认语言（中文）
- 确保默认语言的翻译完整且准确

### 4. 维护性

- 定期检查翻译的准确性
- 添加新功能时同步更新所有语言
- 使用专业翻译服务确保质量

## 故障排查

### 1. 语言不生效

**检查项**：
- 请求头 `Accept-Language` 是否正确设置
- 语言代码是否在支持列表中
- 资源文件是否存在对应的翻译

### 2. 参数替换错误

**检查项**：
- 占位符格式是否正确（{0}, {1}）
- 参数数量是否匹配
- 参数传递是否正确

### 3. 翻译缺失

**检查项**：
- 资源文件中是否有对应的Key
- Key的拼写是否正确
- 文件编码是否为UTF-8

## 总结

通过多语言支持，OSS上传接口现在可以为全球用户提供本地化的用户体验。系统支持：

1. **自动语言检测**：根据请求头自动选择语言
2. **参数化消息**：支持动态参数的国际化消息
3. **回退机制**：确保在任何情况下都有可用的消息
4. **易于扩展**：可以轻松添加新的语言支持

建议根据用户群体的分布情况，优先完善主要语言的翻译质量。
