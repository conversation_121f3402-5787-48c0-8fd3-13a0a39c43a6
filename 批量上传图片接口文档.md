# 批量上传图片接口文档

## 接口概述

新增了批量上传图片到OSS的接口，支持一次性上传多张图片，提高用户体验。

## 接口信息

### 单图上传接口（原有）
- **URL**: `/api/oss/uploadImg`
- **方法**: POST
- **功能**: 上传单张图片

### 批量上传接口（新增）

#### 宽松模式（推荐）
- **URL**: `/api/oss/uploadImgBatch`
- **方法**: POST
- **功能**: 批量上传图片（数量可配置，超出时只处理前N张）

#### 严格模式
- **URL**: `/api/oss/uploadImgBatchStrict`
- **方法**: POST
- **功能**: 批量上传图片（数量可配置，超出时直接拒绝）

### 配置信息接口（新增）
- **URL**: `/api/oss/getUploadConfig`
- **方法**: GET
- **功能**: 获取当前上传配置信息（无需登录）

## 请求参数

### 表单参数
- **file**: 图片文件（支持多个文件）
- **Content-Type**: `multipart/form-data`

### 请求头
- **X-Access-Token**: 用户登录token（必需）
- **Accept-Language**: 语言设置（可选，支持多语言响应）

## 限制条件

### 文件限制（可配置）
- **支持格式**: jpg、jpeg、png、bmp
- **单文件大小**: 可配置（默认10MB）
- **批量数量**: 可配置（默认10张图片）
- **文件内容**: 必须是有效的图片文件

### 配置说明
系统支持通过配置文件动态调整上传限制：

**配置文件**: `src/main/resources/common_config.txt`
```properties
# 批量上传图片最大数量
oss.batch.upload.maxCount=10
# 单个图片文件最大大小（MB）
oss.single.upload.maxSizeMB=10
```

**配置项说明**:
- `oss.batch.upload.maxCount`: 批量上传最大文件数量（默认10）
- `oss.single.upload.maxSizeMB`: 单个文件最大大小，单位MB（默认10）

## 多语言支持

### 支持的语言
- **zh-CN**: 简体中文（默认）
- **en-US**: 英语
- **ja-JP**: 日语
- **zh-HK**: 繁体中文（香港）
- **zh-TW**: 繁体中文（台湾）
- **ko-KR**: 韩语
- **fr-FR**: 法语
- **de-DE**: 德语
- **it-IT**: 意大利语
- **pt-PT**: 葡萄牙语
- **es-ES**: 西班牙语
- **ru-RU**: 俄语
- **th-TH**: 泰语
- **vi-VN**: 越南语

### 使用方式
在请求头中设置 `Accept-Language` 字段：
```
Accept-Language: en-US
```

### 多语言响应示例

#### 中文响应
```json
{
  "state": "fail",
  "msg": "文件过大，最大支持10MB"
}
```

#### 英文响应
```json
{
  "state": "fail",
  "msg": "File too large, maximum 10MB supported"
}
```

#### 日文响应
```json
{
  "state": "fail",
  "msg": "ファイルが大きすぎます。最大10MBまでサポートされています"
}
```

### 限流限制
- **用户级限流**: 每分钟3次
- **IP级限流**: 每分钟20次

## 响应格式

### 成功响应（全部成功）
```json
{
  "state": "ok",
  "data": [
    "https://img.ycjqb.com/img/tattoogo/123456/uuid1.jpg",
    "https://img.ycjqb.com/img/tattoogo/123456/uuid2.jpg"
  ],
  "message": "所有文件上传成功"
}
```

### 成功响应（部分成功）
```json
{
  "state": "ok",
  "data": [
    "https://img.ycjqb.com/img/tattoogo/123456/uuid1.jpg"
  ],
  "failedFiles": [
    "文件2: 文件类型不正确，只支持jpg、jpeg、png、bmp"
  ],
  "message": "部分文件上传成功，成功: 1个，失败: 1个"
}
```

### 失败响应（全部失败）
```json
{
  "state": "fail",
  "msg": "所有文件上传失败",
  "failedFiles": [
    "文件1: 文件过大，最大支持10MB",
    "文件2: 文件类型不正确，只支持jpg、jpeg、png、bmp"
  ]
}
```

### 错误响应
```json
{
  "state": "fail",
  "msg": "错误信息"
}
```

### 配置信息响应
```json
{
  "state": "ok",
  "maxFileCount": 10,
  "maxFileSizeMB": 10,
  "maxFileSizeBytes": 10485760,
  "supportedFormats": ["jpg", "jpeg", "png", "bmp"],
  "configInfo": "批量上传最多支持10张图片，单张图片最大10MB"
}
```

## 前端使用示例

### HTML表单
```html
<form id="uploadForm" enctype="multipart/form-data">
  <input type="file" name="file" multiple accept="image/*" id="fileInput">
  <button type="submit">批量上传</button>
</form>
```

### JavaScript (使用fetch)
```javascript
async function uploadImages() {
  const fileInput = document.getElementById('fileInput');
  const files = fileInput.files;

  if (files.length === 0) {
    alert('请选择图片文件');
    return;
  }

  if (files.length > 10) {
    alert('最多只能选择10张图片');
    return;
  }

  const formData = new FormData();
  for (let i = 0; i < files.length; i++) {
    formData.append('file', files[i]);
  }

  try {
    const response = await fetch('/api/oss/uploadImgBatch', {
      method: 'POST',
      headers: {
        'X-Access-Token': 'your-token-here',
        'Accept-Language': 'en-US'  // 设置语言
      },
      body: formData
    });

    const result = await response.json();

    if (result.state === 'ok') {
      console.log('上传成功的图片:', result.data);
      if (result.failedFiles) {
        console.log('上传失败的文件:', result.failedFiles);
      }
    } else {
      console.error('上传失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}
```

### 多语言动态切换示例
```javascript
// 获取用户语言偏好
function getUserLanguage() {
  return localStorage.getItem('userLanguage') ||
         navigator.language ||
         'zh-CN';
}

// 上传文件（支持多语言）
async function uploadWithLanguage(files, language = null) {
  const userLang = language || getUserLanguage();

  const formData = new FormData();
  files.forEach(file => formData.append('file', file));

  const response = await fetch('/api/oss/uploadImgBatch', {
    method: 'POST',
    headers: {
      'X-Access-Token': getToken(),
      'Accept-Language': userLang
    },
    body: formData
  });

  return await response.json();
}

// 使用示例
uploadWithLanguage(files, 'ja-JP').then(result => {
  if (result.state === 'ok') {
    console.log('アップロード成功!');
  } else {
    console.error('エラー:', result.msg);
  }
});
```

### jQuery示例
```javascript
$('#uploadForm').on('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  
  $.ajax({
    url: '/api/oss/uploadImgBatch',
    type: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    headers: {
      'X-Access-Token': 'your-token-here'
    },
    success: function(result) {
      if (result.state === 'ok') {
        console.log('上传成功的图片:', result.data);
        if (result.failedFiles) {
          console.log('上传失败的文件:', result.failedFiles);
        }
      } else {
        alert('上传失败: ' + result.msg);
      }
    },
    error: function() {
      alert('请求失败');
    }
  });
});
```

## 安全特性

1. **用户认证**: 必须登录才能使用
2. **文件类型验证**: 只允许图片格式
3. **文件大小限制**: 单文件最大10MB
4. **文件内容验证**: 防止伪造图片文件
5. **数量限制**: 最多10张图片
6. **限流保护**: 防止恶意请求
7. **文件名安全**: 使用UUID重命名文件
8. **用户隔离**: 文件按用户ID分目录存储

## 注意事项

1. 批量上传比单图上传有更严格的限流限制
2. 如果部分文件上传失败，成功的文件仍会返回URL
3. 临时文件会在处理完成后自动清理
4. 建议前端在上传前进行文件格式和大小的预检查
5. 大量文件上传时建议显示进度条提升用户体验
