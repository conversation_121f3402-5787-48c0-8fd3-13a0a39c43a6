package com.sandu.xinye.api.oss;

import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * OSS服务批量上传测试
 */
public class OssServiceTest {

    private OssService ossService;

    @Mock
    private UploadFile mockUploadFile1;

    @Mock
    private UploadFile mockUploadFile2;

    @Mock
    private File mockFile1;

    @Mock
    private File mockFile2;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ossService = OssService.me;
    }

    @Test
    public void testUploadImgBatch_EmptyFileList() {
        // 测试空文件列表
        List<UploadFile> emptyList = new ArrayList<>();
        RetKit result = ossService.uploadImgBatch(emptyList, "testUser");
        
        assertEquals("fail", result.getStr("state"));
        assertEquals("上传图片不能为空！", result.getStr("msg"));
    }

    @Test
    public void testUploadImgBatch_NullFileList() {
        // 测试null文件列表
        RetKit result = ossService.uploadImgBatch(null, "testUser");
        
        assertEquals("fail", result.getStr("state"));
        assertEquals("上传图片不能为空！", result.getStr("msg"));
    }

    @Test
    public void testUploadImgBatch_EmptyUserId() {
        // 测试空用户ID
        List<UploadFile> files = new ArrayList<>();
        files.add(mockUploadFile1);
        
        RetKit result = ossService.uploadImgBatch(files, "");
        
        assertEquals("fail", result.getStr("state"));
        assertEquals("用户ID不能为空！", result.getStr("msg"));
    }

    @Test
    public void testUploadImgBatch_NullUserId() {
        // 测试null用户ID
        List<UploadFile> files = new ArrayList<>();
        files.add(mockUploadFile1);
        
        RetKit result = ossService.uploadImgBatch(files, null);
        
        assertEquals("fail", result.getStr("state"));
        assertEquals("用户ID不能为空！", result.getStr("msg"));
    }

    @Test
    public void testUploadImgBatch_TooManyFiles() {
        // 测试文件数量超限（超过10个）
        List<UploadFile> files = new ArrayList<>();
        for (int i = 0; i < 11; i++) {
            files.add(mockUploadFile1);
        }
        
        RetKit result = ossService.uploadImgBatch(files, "testUser");
        
        assertEquals("fail", result.getStr("state"));
        assertEquals("批量上传最多支持10张图片", result.getStr("msg"));
    }

    @Test
    public void testUploadImgBatch_ValidFileCount() {
        // 测试有效的文件数量（10个以内）
        List<UploadFile> files = new ArrayList<>();
        
        // 模拟有效的图片文件
        when(mockUploadFile1.getFileName()).thenReturn("test1.jpg");
        when(mockUploadFile1.getFile()).thenReturn(mockFile1);
        when(mockFile1.length()).thenReturn(1024L * 1024L); // 1MB
        
        when(mockUploadFile2.getFileName()).thenReturn("test2.png");
        when(mockUploadFile2.getFile()).thenReturn(mockFile2);
        when(mockFile2.length()).thenReturn(2 * 1024L * 1024L); // 2MB
        
        files.add(mockUploadFile1);
        files.add(mockUploadFile2);
        
        // 注意：这个测试可能会因为实际的OSS上传而失败
        // 在实际环境中，建议使用Mock来模拟AliOssKit.upload方法
        RetKit result = ossService.uploadImgBatch(files, "testUser");
        
        // 验证结果不为null
        assertNotNull(result);
        assertNotNull(result.getStr("state"));
    }

    /**
     * 测试单个文件上传的参数验证
     */
    @Test
    public void testUploadImg_NullFile() {
        RetKit result = ossService.uploadImg(null, "testUser");
        
        assertEquals("fail", result.getStr("state"));
        assertEquals("上传图片不能为空！", result.getStr("msg"));
    }

    @Test
    public void testUploadImg_EmptyUserId() {
        RetKit result = ossService.uploadImg(mockUploadFile1, "");
        
        assertEquals("fail", result.getStr("state"));
        assertEquals("用户ID不能为空！", result.getStr("msg"));
    }

    @Test
    public void testUploadImg_NullUserId() {
        RetKit result = ossService.uploadImg(mockUploadFile1, null);
        
        assertEquals("fail", result.getStr("state"));
        assertEquals("用户ID不能为空！", result.getStr("msg"));
    }
}
