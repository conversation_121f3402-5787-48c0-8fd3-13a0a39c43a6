package com.sandu.xinye.admin.help;

import com.jfinal.kit.Kv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.HelpCenter;
import com.sandu.xinye.common.model.Material;

import java.util.ArrayList;
import java.util.Date;

public class HelpService {
    public static final HelpService me = new HelpService();

    public RetKit list(int pageSize, int pageNumber, Kv kv) {
        SqlPara sqlPara = Db.getSqlPara("admin.helpcenter.paginate", kv);
        Page<HelpCenter> page = HelpCenter.dao.paginate(pageNumber, pageSize, sqlPara);
        return RetKit.ok("data", page);
    }

    public RetKit addHelp(HelpCenter helpCenter) {
        boolean succ =helpCenter.setCreateTime(new Date()).save();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit deleteHelp(String id) {
        String[] ids = id.split(",");
        ArrayList<Integer> failItems = new ArrayList<>();
        for (String mId : ids) {
            HelpCenter helpCenter = HelpCenter.dao.findById(Integer.parseInt(mId));
            boolean succ = helpCenter.delete();
            if (!succ) {
                failItems.add(1);
            }
        }
        return  failItems.isEmpty() ? RetKit.ok().setOk("删除成功") : RetKit.fail("删除失败" + failItems.size() + "个");
    }

    public RetKit updateHelp(HelpCenter helpCenter) {
        boolean succ = helpCenter.update();
        return succ ? RetKit.ok() : RetKit.fail();
    }


}
