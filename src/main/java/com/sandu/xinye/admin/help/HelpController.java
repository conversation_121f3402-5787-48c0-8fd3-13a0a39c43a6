package com.sandu.xinye.admin.help;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.admin.material.MaterialService;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.HelpCenter;
import com.sandu.xinye.common.model.Material;


public class HelpController extends AdminController {
	
	@Clear
	public void index(){
		render("/admin_index.html");
	}
	

	public void list(){
		int pageNumber = getParaToInt("pageNumber",1);
		int pageSize = getParaToInt("pageSize",10);
		RetKit ret = HelpService.me.list(pageSize, pageNumber,getParaToMap());
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void add(@JsonBody HelpCenter helpCenter){
		RetKit ret = HelpService.me.addHelp(helpCenter);
		renderJson(ret);
	}

	public void deleteHelp(){
		String id = getPara("id","");
		RetKit ret = HelpService.me.deleteHelp(id);
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void updateHelp(@JsonBody HelpCenter helpCenter){
		RetKit ret = HelpService.me.updateHelp(helpCenter);
		renderJson(ret);
	}


}
