package com.sandu.xinye.admin.font;

import com.jfinal.kit.Kv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.sandu.xinye.common.model.Font;
import com.sandu.xinye.common.model.FontLocale;
import com.xiaoleilu.hutool.util.StrUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class FontService {
    public static final FontService me = new FontService();

    public RetKit list(int pageSize, int pageNumber, Kv kv) {
        if (kv.containsKey("isNew")) {
            // isNew和locale是互斥的
            kv.remove("locale");
        }

        SqlPara sqlPara = Db.getSqlPara("admin.font.paginate", kv);
        Page<Font> page = Font.dao.paginate(pageNumber, pageSize, sqlPara);

        return RetKit.ok("data", page);
    }

    public RetKit add(Font font) {
        boolean succ = font.setFontValue(font.getFontName()).setIsNew(font.getIsNew()).save();
        if (succ) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.FONT);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit edit(Integer id, Font font) {
        if (id == null || font == null) {
            return RetKit.fail("传参错误!");
        }
        if (font.getFontName() != null) {
            font.setFontValue(font.getFontName());
        }
        boolean succ = font.setId(id).setCreateTime(new Date()).setIsNew(font.getIsNew()).update();
        if (succ) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.FONT);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit delete(Integer id) {
        if (id == null) {
            return RetKit.fail("传参错误!");
        }
        Font font = Font.dao.findById(id);
        if (font == null) {
            return RetKit.fail("字体不存在!");
        }
        boolean succ = font.delete();
        if (succ) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.FONT);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit del(String ids) {
        if (StrUtil.isEmpty(ids)) {
            return RetKit.fail("传参错误!");
        }
        String[] idArr = ids.split(",");
        boolean hasSuccess = false;

        if (idArr.length == 1) {
            // 查找id所在的数据是否存在，不存在，提示数据不存在
            Integer id = Integer.parseInt(idArr[0]);
            Font font = Font.dao.findById(id);
            if (font == null) {
                return RetKit.fail("数据不存在!");
            }

            boolean succ = Font.dao.deleteById(id);
            if (succ) {
                hasSuccess = true;
            }
            if (hasSuccess) {
                // 清除缓存
                CacheKit.removeAll(CacheConstant.FONT);
            }
            return succ ? RetKit.ok() : RetKit.fail();
        } else {
            // 根据idArr, 批量删除
            String sql = "DELETE FROM font WHERE id IN (" + String.join(",", idArr) + ")";
            try {
                int result = Db.delete(sql);
                if (result > 0) {
                    // 清除缓存
                    CacheKit.removeAll(CacheConstant.FONT);
                }
                return result > 0 ? RetKit.ok() : RetKit.fail();
            } catch (Exception e) {
                return RetKit.fail();
            }
        }
    }

    public RetKit localeList() {
        // 生成缓存key
        String cacheKey = CacheConstant.FONT_LOCALE_LIST + "_all";

        // 尝试从缓存获取
        List<FontLocale> cachedList = CacheKit.get(CacheConstant.FONT, cacheKey);
        if (cachedList != null) {
            return RetKit.ok("data", cachedList);
        }

        List<FontLocale> list = FontLocale.dao.find("select locale, name from font_locale");

        // 存入缓存
        CacheKit.put(CacheConstant.FONT, cacheKey, list);

        return RetKit.ok("data", list);
    }

}
