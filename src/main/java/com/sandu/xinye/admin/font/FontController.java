package com.sandu.xinye.admin.font;

import com.jfinal.aop.Before;
import com.sandu.xinye.admin.material.MaterialService;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Font;
import com.sandu.xinye.common.model.MaterialGroup;

public class FontController extends AppController {

    public void list() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        RetKit ret = FontService.me.list(pageSize, pageNumber, getParaToMap());
        renderJson(ret);
    }

    @Before({PostOnlyInterceptor.class})
    public void add(@JsonBody Font font) {
        RetKit ret = FontService.me.add(font);
        renderJson(ret);
    }

    @Before({PostOnlyInterceptor.class})
    public void edit(@JsonBody Font font) {
        Integer id = getParaToInt("id");
        RetKit ret = FontService.me.edit(id, font);
        renderJson(ret);
    }

    @Before({PostOnlyInterceptor.class})
    public void del() {
        String ids = getPara("id");
        RetKit ret = FontService.me.del(ids);
        renderJson(ret);
    }

    public void locale() {
        RetKit ret = FontService.me.localeList();
        renderJson(ret);
    }
}
