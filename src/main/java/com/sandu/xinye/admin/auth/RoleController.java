package com.sandu.xinye.admin.auth;


import java.util.List;

import com.jfinal.aop.Before;
import com.sandu.xinye.admin.model.RoleMenu;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.SysUserRole;

public class RoleController extends AdminController {

	public void getRoleList() {
		RetKit ret = RoleService.me.getRoleList();
		renderJson(ret);
	}
	
	public void getMenuList(){
		renderJson(RetKit.ok("list", RoleService.me.findMenus()));
	}

	@Before({PostOnlyInterceptor.class})
	public void save(@JsonBody RoleMenu roleMenu) {
//		List<String> menus = getParaToList("menus");
//		String name = getPara("name");
		RetKit ret = RoleService.me.save(roleMenu.getMenus(),roleMenu.getRole().getName(), getAccount(), getIpAddress());
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void update(@JsonBody RoleMenu roleMenu) {
//		List<String> menus = getParaToList("menus");
//		SysUserRole role = getBean(SysUserRole.class, "");
		RetKit ret = RoleService.me.update( roleMenu.getMenus(), roleMenu.getRole(), getAccount(), getIpAddress());
		renderJson(ret);
	}

	public void remove() {
		String id = getPara("id","");
		renderJson(RoleService.me.remove(Integer.parseInt(id), getAccount(), getIpAddress()));
	}
}
