package com.sandu.xinye.admin.auth;

import com.jfinal.aop.Before;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.SysUser;

public class SysUserController extends AdminController {

	public void list() {
		int pageNumber = getParaToInt("pageNumber", 1);
		int pageSize = getParaToInt("pageSize", 10);
		RetKit ret = SysUserService.me.list(pageNumber, pageSize, getParaToMap());
		renderJson(ret);
	}
	
	public void getRoleList(){
		RetKit ret = SysUserService.me.getRoleList();
		renderJson(ret);
	}

	@Before({PostOnlyInterceptor.class})
	public void save(@JsonBody SysUser sysUser) {
		RetKit ret = SysUserService.me.save(sysUser, getAccount(), getIpAddress());
		renderJson(ret);
	}

	@Before({PostOnlyInterceptor.class})
	public void update(@JsonBody SysUser sysUser){
		RetKit ret = SysUserService.me.update(sysUser);
		renderJson(ret);
	}
	
	public void del(){
		String id = getPara("userId","");
		RetKit ret = SysUserService.me.del(id,getAccount(),getIpAddress());
		renderJson(ret);
	}
}
