package com.sandu.xinye.admin.oss;

import com.jfinal.aop.Before;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.kit.RetKit;

import java.util.List;

public class OssController extends AppController {
    public void uploadImg() {
        List<UploadFile> ufs = getFiles("file");
        RetKit ret = OssService.me.uploadImg(ufs);
        renderJson(ret);
    }

    public void uploadFile() {
        List<UploadFile> ufs = getFiles("file");
        RetKit ret = OssService.me.uploadImg(ufs);
        renderJson(ret);
    }

}
