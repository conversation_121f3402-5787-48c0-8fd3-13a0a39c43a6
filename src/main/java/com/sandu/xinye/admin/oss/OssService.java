package com.sandu.xinye.admin.oss;

import com.jfinal.kit.LogKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.constant.OssConstant;
import com.sandu.xinye.common.kit.*;
import com.xiaoleilu.hutool.util.CollectionUtil;

import java.util.List;
import java.util.stream.Collectors;



public class OssService {
    public static final OssService me = new OssService();

    private static final String OSS_OBJECT_PREFIX_IMG = "img/tattoogo";
    private static final String OSS_OBJECT_PREFIX_FILE = "file/tattoogo";

    /**
     *
     * 阿里云OSS上传图片
     *
     * @param uf
     * @return
     */
    public RetKit uploadImg(UploadFile uf) {
        LogKit.info("uploadImg=== 上传图片");
        if (uf == null) {
            LogKit.error("=== 上传图片不能为空");
            return RetKit.fail("The uploaded image cannot be empty!");
        }
        try {
            if (!ImageKit.isImageExtName(uf.getFileName())) {
                LogKit.error("uploadImg=== 文件类型不正确，只支持类型：jpg、jpeg、png、bmp");
                return RetKit.fail("msg", "The file type is incorrect. Only supported types are allowed：jpg、jpeg、png、bmp");
            }

            String ossFileUrl = AliOssKit.upload(OSS_OBJECT_PREFIX_IMG, uf);

            return RetKit.ok().set("data", ossFileUrl);
        } catch (Exception e) {
            LogKit.error("Upload file failed：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            LogKit.info("清除临时文件" + uf.getFile().getName());
            uf.getFile().delete();
        }
    }

    public RetKit uploadImg(List<UploadFile> ufs) {
        LogKit.info("uploadImg=== 上传图片");
        if (CollectionUtil.isEmpty(ufs)) {
            LogKit.error("=== 上传图片不能为空");
            return RetKit.fail("The uploaded image cannot be empty!");
        }
        try {
            if(ufs.size() == 1){
                String ossFileUrl = AliOssKit.upload(OSS_OBJECT_PREFIX_IMG, ufs.get(0));
                return RetKit.ok().set("data", ossFileUrl);
            }else {
                List<String> ossFileUrl = AliOssKit.upload(OSS_OBJECT_PREFIX_IMG, ufs);
                return RetKit.ok().set("data", ossFileUrl.toArray());
            }
        } catch (Exception e) {
            LogKit.error("Upload file failed：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            LogKit.info("清除临时文件: " + ufs.stream().map(uf-> uf.getFile().getName()).collect(Collectors.joining(",")));
            ufs.stream().map(uf-> uf.getFile().delete());
        }
    }

    public RetKit uploadFile(List<UploadFile> ufs) {
        LogKit.info("uploadFile=== 上传文件");
        if (CollectionUtil.isEmpty(ufs)) {
            LogKit.error("=== 上传文件不能为空");
            return RetKit.fail("The uploaded image cannot be empty!");
        }
        try {
            if(ufs.size() == 1){
                String ossFileUrl = AliOssKit.upload(OSS_OBJECT_PREFIX_FILE, ufs.get(0));
                return RetKit.ok().set("data", ossFileUrl);
            }else {
                List<String> ossFileUrl = AliOssKit.upload(OSS_OBJECT_PREFIX_FILE, ufs);
                return RetKit.ok().set("data", ossFileUrl.toArray());
            }
        } catch (Exception e) {
            LogKit.error("Upload file failed：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            LogKit.info("清除临时文件: " + ufs.stream().map(uf-> uf.getFile().getName()).collect(Collectors.joining(",")));
            ufs.stream().map(uf-> uf.getFile().delete());
        }
    }

    /**
     * 根据ossUrl获取文件名ossName
     */
    private static String getOssName(String ossUrl) {
        return ossUrl.replace(OssConstant.CDN_DOMAIN, "");
    }

    private static String getOssPrefix(String ossUrl) {
        String ossName = getOssName(ossUrl);
        return ossName.substring(0, ossName.lastIndexOf("/") + 1);
    }

}