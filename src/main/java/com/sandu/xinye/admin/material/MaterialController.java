package com.sandu.xinye.admin.material;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.admin.model.GroupSort;
import com.sandu.xinye.admin.model.Materials;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Material;
import com.sandu.xinye.common.model.MaterialGroup;
import com.sandu.xinye.common.model.TempletGroup;

import java.util.ArrayList;

public class MaterialController extends AdminController {
	
	@Clear
	public void index(){
		render("/admin_index.html");
	}
	
	public void list(){
		int pageNumber = getParaToInt("pageNumber",1);
		int pageSize = getParaToInt("pageSize",10);
		RetKit ret = MaterialService.me.list(pageSize, pageNumber,getParaToMap());
		renderJson(ret);
	}
	
	public void add(){
		MaterialGroup tgroup = getBean(MaterialGroup.class,"");
		RetKit ret = MaterialService.me.add(tgroup);
		renderJson(ret);
	}

	
	public void deleteGroup(){
		int groupId = getParaToInt("id",-1);
		RetKit ret = MaterialService.me.deleteGroup(groupId);
		renderJson(ret);
	}

	public void getGroupList(){
		RetKit ret = MaterialService.me.getGroupList();
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void addGroup(@JsonBody MaterialGroup group) {
		RetKit ret = MaterialService.me.addGroup(group);
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void updateGroup(@JsonBody MaterialGroup group) {
		RetKit ret = MaterialService.me.updateGroup(group);
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void sortGroup(@JsonBody GroupSort  groupSort) {
		RetKit ret = MaterialService.me.sortGroup(groupSort.getSorts());
		renderJson(ret);
	}

	public void hasMaterial(){
		int groupId = getParaToInt("groupId",-1);
		RetKit ret = MaterialService.me.hasMaterial(groupId);
		renderJson(ret);
	}
	public void deleteMaterial(){
		String id = getPara("id","");
		RetKit ret = MaterialService.me.deleteMaterial(id);
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void updateMaterial(@JsonBody Material material){
		RetKit ret = MaterialService.me.updateMaterial(material);
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void addMaterial(@JsonBody Material material){
		RetKit ret = MaterialService.me.addMaterial(material);
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void importMaterial(@JsonBody Materials materials){
		RetKit ret = MaterialService.me.importMaterial(materials.getMaterials());
		renderJson(ret);
	}








}
