package com.sandu.xinye.admin.material;

import com.jfinal.aop.Before;
import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.admin.model.GroupSort;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.sandu.xinye.common.model.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MaterialService {
    public static final MaterialService me = new MaterialService();

    public RetKit list(int pageSize, int pageNumber, Kv kv) {
        String groupId = kv.getStr("groupId");
        Kv newKv = new Kv();
        if (groupId.equals("-100")) {
            newKv.set("is_new", "1");
        } else if (!groupId.equals("-1")) {
            newKv.set("groupId", groupId);
        }
        SqlPara sqlPara = Db.getSqlPara("admin.material.paginate", newKv);
        Page<Material> page = Material.dao.paginate(pageNumber, pageSize, sqlPara);
        // 遍历 Page 中的每个 Material 对象
        for (Material material : page.getList()) {
            // 获取当前 Material 对象的 url
            String originalUrl = material.getUrlPreview();
            // 拼接缩略图参数到 url 上
            String thumbnailUrl = originalUrl + "?x-oss-process=image/resize,w_200";
            // 更新 Font 对象中的 url 字段
            material.set("url_preview", thumbnailUrl);
        }
        return RetKit.ok("page", page);
    }

    public RetKit add(MaterialGroup tgroup) {
        boolean succ = tgroup.setSort(0).setCode(tgroup.getName()).setStatus(0).save();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit addGroup(MaterialGroup tgroup) {
        boolean succ = tgroup.setStatus(0).save();
        if (succ) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.MATERIAL);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit updateGroup(MaterialGroup tgroup) {
        boolean succ = tgroup.update();
        if (succ) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.MATERIAL);
        }
        return succ ? RetKit.ok().setOk("编辑成功") : RetKit.fail();
    }

    public RetKit deleteGroup(int id) {
        MaterialGroup tgroup = MaterialGroup.dao.findById(id);
        boolean succ = tgroup.delete();
        if (succ) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.MATERIAL);
        }
        return succ ? RetKit.ok().setOk("删除成功") : RetKit.fail();
    }

    public RetKit getGroupList() {
        String sql = String.format("select * from material_group  order by sort asc");
        List<MaterialGroup> list = MaterialGroup.dao.find(sql);
        return RetKit.ok("list", list);
    }

    public RetKit sortGroup(ArrayList<GroupSort.GSort> sorts) {
        // 生成 SQL 语句
        String batchUpdateSQL = generateBatchUpdateSQL(sorts);
        boolean succ1 = Db.update(batchUpdateSQL) > 1;
        return succ1 ? RetKit.ok() : RetKit.fail();
    }

    // 生成批量更新的 SQL
    public static String generateBatchUpdateSQL(ArrayList<GroupSort.GSort> sorts) {
        StringBuilder sql = new StringBuilder("UPDATE material_group SET sort = CASE ");
        // 构建 CASE 语句
        for (GroupSort.GSort item : sorts) {
            sql.append("WHEN id = ").append(item.getId())
                    .append(" THEN ").append(item.getSort()).append(" ");
        }
        // 添加 WHERE 子句，限制 id 的更新
        sql.append("END WHERE id IN (");
        for (int i = 0; i < sorts.size(); i++) {
            sql.append(sorts.get(i).getId());
            if (i < sorts.size() - 1) {
                sql.append(", ");
            }
        }
        sql.append(");");
        return sql.toString();
    }

    /**
     * 分类下是否存在素材
     *
     * @param groupId
     * @return
     */
    public RetKit hasMaterial(int groupId) {
        Material material = Material.dao.findFirst("select * from material where groupId=? ", groupId);
        boolean isExist = material != null ? true : false;
        return RetKit.ok("isExist", isExist);
    }

    public RetKit addMaterial(Material material) {
        boolean succ = material.setCreateTime(new Date()).save();
        if (succ) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.MATERIAL);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit importMaterial(ArrayList<Material> materials) {
        ArrayList<Integer> failItems = new ArrayList<>();
        boolean hasSuccess = false;
        for (Material material : materials) {
            boolean succ = material.setCreateTime(new Date()).save();
            if (!succ) {
                failItems.add(1);
            } else {
                hasSuccess = true;
            }
        }
        if (hasSuccess) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.MATERIAL);
        }
        return failItems.isEmpty() ? RetKit.ok() : RetKit.fail("导入失败" + failItems.size() + "个");
    }

    public RetKit deleteMaterial(String id) {
        String[] ids = id.split(",");
        ArrayList<Integer> failItems = new ArrayList<>();
        boolean hasSuccess = false;
        for (String mId : ids) {
            Material material = Material.dao.findById(Integer.parseInt(mId));
            boolean succ = material.delete();
            if (!succ) {
                failItems.add(1);
            } else {
                hasSuccess = true;
            }
        }
        if (hasSuccess) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.MATERIAL);
        }
        return failItems.isEmpty() ? RetKit.ok().setOk("删除成功") : RetKit.fail("删除失败" + failItems.size() + "个");
    }

    public RetKit updateMaterial(Material material) {
        boolean succ = material.update();
        if (succ) {
            // 清除缓存
            CacheKit.removeAll(CacheConstant.MATERIAL);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

}
