package com.sandu.xinye.admin.operate;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.OperationLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OperationLogService {
	private static  final Logger logger = LoggerFactory.getLogger(OperationLogService.class);

	public static final OperationLogService me = new OperationLogService();
	
	public boolean saveOperationLog(Integer sysUserId,String ip, String content){
		OperationLog op = new OperationLog();
		boolean succ = op.setSysUserId(sysUserId).setIp(ip).setContent(content).setCreateTime(new Date()).save();
		return succ;
	}


	public void saveOperationLog(Integer UserId, String ip, String modelName, String content) {
		CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
			try {
				OperationLog op = new OperationLog();
				op.setType(Constant.OPERATION_LOG_TYPE_USER).setModelName(modelName).setSysUserId(UserId)
						.setIp(ip).setContent(content).setCreateTime(new Date()).save();
			}catch (Exception ex){
				ex.printStackTrace();
			}
		}).exceptionally(ex -> {
			logger.error("写操作日志异常: " + ex.getMessage());
			return null;
		});

	}
	
	public RetKit page(int pageNumber ,int pageSize){
		SqlPara sqlPara = Db.getSqlPara("admin.operate.paginate");
		Page<OperationLog> page = OperationLog.dao.paginate(pageNumber, pageSize, sqlPara);
		return RetKit.ok("page",page);
		
	}
	
}
