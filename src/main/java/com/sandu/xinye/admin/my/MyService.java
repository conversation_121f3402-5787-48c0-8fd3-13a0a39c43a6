package com.sandu.xinye.admin.my;

import com.jfinal.kit.HashKit;
import com.jfinal.plugin.activerecord.Db;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.SysUser;
import org.apache.log4j.Logger;

import java.util.concurrent.atomic.AtomicReference;


public class MyService {
    public static final MyService me = new MyService();

    private static final Logger logger = Logger.getLogger(AttackAntiInterceptor.class);

    public RetKit resetPassword(String oldPassword, String newPassword, SysUser account, String ip) {
        if (account.getSysUserRoleId() == 0) {
            return RetKit.fail("超级管理员账号不允许更改");
        }

        AtomicReference<String> salt = new AtomicReference<>(account.getSalt());
        String oldPwd = HashKit.sha256(salt + oldPassword);
        if (!account.getSysUserPass().equals(oldPwd)) {
            return RetKit.fail("旧密码不正确！");
        }

        boolean succ= Db.tx(() -> {
            // 修改成新密码
            salt.set(HashKit.generateSaltForSha256());
            String newPwd = HashKit.sha256(salt + newPassword);
            boolean updSucc = account.setSalt(salt.get()).setSysUserPass(newPwd).update();
            if (updSucc) {
                removeCacheAndSession(account.getSysUserId());
                String content = account.getSysUserName() + "修改了密码" ;
                logger.debug(account.getSysUserName() + "修改了密码，新密码为：" + newPassword);
                OperationLogService.me.saveOperationLog(account.getSysUserId(), ip, content);
            }

            return updSucc;
        });

        return succ ? RetKit.ok() : RetKit.fail();
    }

    private void removeCacheAndSession(Integer userId) {
        Db.update("delete from sys_user_session where sysUserId=?", userId);
    }

}
