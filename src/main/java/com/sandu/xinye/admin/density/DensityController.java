package com.sandu.xinye.admin.density;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Density;


public class DensityController extends AdminController {
	
	@Clear
	public void index(){
		render("/admin_index.html");
	}
	
	public void list(){
		int pageNumber = getParaToInt("pageNumber",1);
		int pageSize = getParaToInt("pageSize",10);
		RetKit ret = DensityService.me.list(pageSize, pageNumber,getParaToMap());
		renderJson(ret);
	}
	public void hasDensity(){
		int groupId = getParaToInt("groupId",-1);
		RetKit ret = DensityService.me.hasDensity(groupId);
		renderJson(ret);
	}
	public void deleteDensity(){
		String id = getPara("id","");
		RetKit ret = DensityService.me.deleteDensity(id);
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void updateDensity(@JsonBody Density density){
		RetKit ret = DensityService.me.updateDensity(density);
		renderJson(ret);
	}
	@Before({PostOnlyInterceptor.class})
	public void addDensity(@JsonBody Density density){
		RetKit ret = DensityService.me.addDensity(density);
		renderJson(ret);
	}
}
