package com.sandu.xinye.admin.density;

import com.jfinal.kit.Kv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Density;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DensityService {
    public static final DensityService me = new DensityService();

    public RetKit list(int pageSize, int pageNumber, Kv kv) {
        String groupId = kv.getStr("groupId");
        SqlPara sqlPara = Db.getSqlPara("admin.density.paginate", kv);
        Page<Density> page = Density.dao.paginate(pageNumber, pageSize, sqlPara);
        return RetKit.ok("page", page);
    }



    /**
     * 分类下是否存在素材
     *
     * @param groupId
     * @return
     */
    public RetKit hasDensity(int groupId) {
        Density density =Density.dao. findFirst("select * from Density where groupId=? ", groupId);
        boolean isExist = density != null ? true : false;
        return RetKit.ok("isExist", isExist);
    }

    public RetKit addDensity(Density density) {
        boolean succ = density.setStatus(1).setCreateTime(new Date()).save();
        return succ ? RetKit.ok() : RetKit.fail();
    }



    public RetKit deleteDensity(String id) {
        String[] ids = id.split(",");
        ArrayList<Integer> failItems = new ArrayList<>();
        for (String mId : ids) {
            Density density = Density.dao.findById(Integer.parseInt(mId));
            boolean succ = density.delete();
            if (!succ) {
                failItems.add(1);
            }
        }
        return  failItems.isEmpty() ? RetKit.ok().setOk("删除成功") : RetKit.fail("删除失败" + failItems.size() + "个");
    }

    public RetKit updateDensity(Density density) {
        boolean succ = density.update();
        return succ ? RetKit.ok() : RetKit.fail();
    }

}
