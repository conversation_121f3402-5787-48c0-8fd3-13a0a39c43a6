package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseTempletGroup<M extends BaseTempletGroup<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setName(java.lang.String name) {
		set("name", name);
		return (M)this;
	}
	
	public java.lang.String getName() {
		return getStr("name");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setIsDel(java.lang.Boolean isDel) {
		set("isDel", isDel);
		return (M)this;
	}
	
	public java.lang.Boolean getIsDel() {
		return get("isDel");
	}

	public M setDeleteTime(java.util.Date deleteTime) {
		set("deleteTime", deleteTime);
		return (M)this;
	}
	
	public java.util.Date getDeleteTime() {
		return get("deleteTime");
	}

	public M setType(java.lang.Integer type) {
		set("type", type);
		return (M)this;
	}
	
	public java.lang.Integer getType() {
		return getInt("type");
	}

	public M setNameRU(java.lang.String nameRU) {
		set("nameRU", nameRU);
		return (M)this;
	}
	
	public java.lang.String getNameRU() {
		return getStr("nameRU");
	}

}
