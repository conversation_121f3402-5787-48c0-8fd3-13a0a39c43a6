package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseUserBind<M extends BaseUserBind<M>> extends Model<M> implements IBean {

	public M setUserBindId(java.lang.Integer userBindId) {
		set("userBindId", userBindId);
		return (M)this;
	}
	
	public java.lang.Integer getUserBindId() {
		return getInt("userBindId");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setBindId(java.lang.String bindId) {
		set("bindId", bindId);
		return (M)this;
	}
	
	public java.lang.String getBindId() {
		return getStr("bindId");
	}

	public M setBindType(java.lang.Integer bindType) {
		set("bindType", bindType);
		return (M)this;
	}
	
	public java.lang.Integer getBindType() {
		return getInt("bindType");
	}

	public M setPlatform(java.lang.Integer platform) {
		set("platform", platform);
		return (M)this;
	}
	
	public java.lang.Integer getPlatform() {
		return getInt("platform");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
