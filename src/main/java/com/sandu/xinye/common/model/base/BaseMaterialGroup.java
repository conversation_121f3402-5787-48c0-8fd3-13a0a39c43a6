package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseMaterialGroup<M extends BaseMaterialGroup<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setSort(java.lang.Integer sort) {
		set("sort", sort);
		return (M)this;
	}
	
	public java.lang.Integer getSort() {
		return getInt("sort");
	}

	public M setStatus(java.lang.Integer status) {
		set("status", status);
		return (M)this;
	}
	
	public java.lang.Integer getStatus() {
		return getInt("status");
	}

	public M setCode(java.lang.String code) {
		set("code", code);
		return (M)this;
	}
	
	public java.lang.String getCode() {
		return getStr("code");
	}

	public M setName(java.lang.String name) {
		set("name", name);
		return (M)this;
	}
	
	public java.lang.String getName() {
		return getStr("name");
	}

	public M setNameCode(java.lang.String nameCode) {
		set("nameCode", nameCode);
		return (M)this;
	}
	
	public java.lang.String getNameCode() {
		return getStr("nameCode");
	}

	public M setNameEn(java.lang.String nameEn) {
		set("nameEn", nameEn);
		return (M)this;
	}
	
	public java.lang.String getNameEn() {
		return getStr("nameEn");
	}

	public M setNameDe(java.lang.String nameDe) {
		set("nameDe", nameDe);
		return (M)this;
	}
	
	public java.lang.String getNameDe() {
		return getStr("nameDe");
	}

	public M setNameFr(java.lang.String nameFr) {
		set("nameFr", nameFr);
		return (M)this;
	}
	
	public java.lang.String getNameFr() {
		return getStr("nameFr");
	}

	public M setNameIt(java.lang.String nameIt) {
		set("nameIt", nameIt);
		return (M)this;
	}
	
	public java.lang.String getNameIt() {
		return getStr("nameIt");
	}

	public M setNamePt(java.lang.String namePt) {
		set("namePt", namePt);
		return (M)this;
	}
	
	public java.lang.String getNamePt() {
		return getStr("namePt");
	}

	public M setNameKo(java.lang.String nameKo) {
		set("nameKo", nameKo);
		return (M)this;
	}
	
	public java.lang.String getNameKo() {
		return getStr("nameKo");
	}

	public M setNameVi(java.lang.String nameVi) {
		set("nameVi", nameVi);
		return (M)this;
	}
	
	public java.lang.String getNameVi() {
		return getStr("nameVi");
	}

	public M setNameTh(java.lang.String nameTh) {
		set("nameTh", nameTh);
		return (M)this;
	}
	
	public java.lang.String getNameTh() {
		return getStr("nameTh");
	}

	public M setNameEs(java.lang.String nameEs) {
		set("nameEs", nameEs);
		return (M)this;
	}
	
	public java.lang.String getNameEs() {
		return getStr("nameEs");
	}

	public M setNameJa(java.lang.String nameJa) {
		set("nameJa", nameJa);
		return (M)this;
	}
	
	public java.lang.String getNameJa() {
		return getStr("nameJa");
	}

	public M setNameZhTw(java.lang.String nameZhTw) {
		set("nameZhTw", nameZhTw);
		return (M)this;
	}
	
	public java.lang.String getNameZhTw() {
		return getStr("nameZhTw");
	}

	public M setNameRu(java.lang.String nameRu) {
		set("nameRu", nameRu);
		return (M)this;
	}
	
	public java.lang.String getNameRu() {
		return getStr("nameRu");
	}

}
