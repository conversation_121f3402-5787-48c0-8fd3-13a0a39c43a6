package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseDensity<M extends BaseDensity<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setMachine(java.lang.String machine) {
		set("machine", machine);
		return (M)this;
	}
	
	public java.lang.String getMachine() {
		return getStr("machine");
	}

	public M setFirmwareVersion(java.lang.String firmwareVersion) {
		set("firmware_version", firmwareVersion);
		return (M)this;
	}
	
	public java.lang.String getFirmwareVersion() {
		return getStr("firmware_version");
	}

	public M setLight(java.lang.Integer light) {
		set("light", light);
		return (M)this;
	}
	
	public java.lang.Integer getLight() {
		return getInt("light");
	}

	public M setMedium(java.lang.Integer medium) {
		set("medium", medium);
		return (M)this;
	}
	
	public java.lang.Integer getMedium() {
		return getInt("medium");
	}

	public M setHeavy(java.lang.Integer heavy) {
		set("heavy", heavy);
		return (M)this;
	}
	
	public java.lang.Integer getHeavy() {
		return getInt("heavy");
	}

	public M setStatus(java.lang.Integer status) {
		set("status", status);
		return (M)this;
	}
	
	public java.lang.Integer getStatus() {
		return getInt("status");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
