package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by <PERSON>F<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseFont<M extends BaseFont<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setFontName(java.lang.String fontName) {
		set("fontName", fontName);
		return (M)this;
	}
	
	public java.lang.String getFontName() {
		return getStr("fontName");
	}

	public M setFontLocale(java.lang.String fontLocale) {
		set("fontLocale", fontLocale);
		return (M)this;
	}
	
	public java.lang.String getFontLocale() {
		return getStr("fontLocale");
	}

	public M setFontUrl(java.lang.String fontUrl) {
		set("fontUrl", fontUrl);
		return (M)this;
	}
	
	public java.lang.String getFontUrl() {
		return getStr("fontUrl");
	}

	public M setFontValue(java.lang.String fontValue) {
		set("fontValue", fontValue);
		return (M)this;
	}
	
	public java.lang.String getFontValue() {
		return getStr("fontValue");
	}

	public M setFontCover(java.lang.String fontCover) {
		set("fontCover", fontCover);
		return (M)this;
	}
	
	public java.lang.String getFontCover() {
		return getStr("fontCover");
	}

	public M setSort(java.lang.Integer sort) {
		set("sort", sort);
		return (M)this;
	}
	
	public java.lang.Integer getSort() {
		return getInt("sort");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setIsNew(java.lang.Boolean isNew) {
		set("isNew", isNew);
		return (M)this;
	}
	
	public java.lang.Boolean getIsNew() {
		return get("isNew");
	}

}
