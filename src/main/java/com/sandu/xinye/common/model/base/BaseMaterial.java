package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseMaterial<M extends BaseMaterial<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setGroupId(java.lang.Long groupId) {
		set("groupId", groupId);
		return (M)this;
	}
	
	public java.lang.Long getGroupId() {
		return getLong("groupId");
	}

	public M setHeigh(java.lang.Integer heigh) {
		set("heigh", heigh);
		return (M)this;
	}
	
	public java.lang.Integer getHeigh() {
		return getInt("heigh");
	}

	public M setWidth(java.lang.Integer width) {
		set("width", width);
		return (M)this;
	}
	
	public java.lang.Integer getWidth() {
		return getInt("width");
	}

	public M setSort(java.lang.Integer sort) {
		set("sort", sort);
		return (M)this;
	}
	
	public java.lang.Integer getSort() {
		return getInt("sort");
	}

	public M setCode(java.lang.String code) {
		set("code", code);
		return (M)this;
	}
	
	public java.lang.String getCode() {
		return getStr("code");
	}

	public M setName(java.lang.String name) {
		set("name", name);
		return (M)this;
	}
	
	public java.lang.String getName() {
		return getStr("name");
	}

	public M setIsNew(java.lang.Boolean isNew) {
		set("is_new", isNew);
		return (M)this;
	}
	
	public java.lang.Boolean getIsNew() {
		return get("is_new");
	}

	public M setUrlPreview(java.lang.String urlPreview) {
		set("url_preview", urlPreview);
		return (M)this;
	}
	
	public java.lang.String getUrlPreview() {
		return getStr("url_preview");
	}

	public M setUrl(java.lang.String url) {
		set("url", url);
		return (M)this;
	}
	
	public java.lang.String getUrl() {
		return getStr("url");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setNameEn(java.lang.String nameEn) {
		set("nameEn", nameEn);
		return (M)this;
	}
	
	public java.lang.String getNameEn() {
		return getStr("nameEn");
	}

}
