package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseUserUnregisterLog<M extends BaseUserUnregisterLog<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setReason(java.lang.String reason) {
		set("reason", reason);
		return (M)this;
	}
	
	public java.lang.String getReason() {
		return getStr("reason");
	}

	public M setIpAddress(java.lang.String ipAddress) {
		set("ipAddress", ipAddress);
		return (M)this;
	}
	
	public java.lang.String getIpAddress() {
		return getStr("ipAddress");
	}

	public M setStatus(java.lang.String status) {
		set("status", status);
		return (M)this;
	}
	
	public java.lang.String getStatus() {
		return getStr("status");
	}

	public M setOperateTime(java.util.Date operateTime) {
		set("operateTime", operateTime);
		return (M)this;
	}
	
	public java.util.Date getOperateTime() {
		return get("operateTime");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
