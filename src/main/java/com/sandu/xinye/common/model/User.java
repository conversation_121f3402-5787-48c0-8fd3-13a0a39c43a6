package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BaseUser;

/**
 * Generated by JFinal.
 */
@SuppressWarnings("serial")
public class User extends BaseUser<User> {
	public static final User dao = new User().dao();

	/*
	 * 1-已注册 2-已登录 99-已注销
	 */
	public static final int STATUS_IS_REGISTER = 1;
	public static final int STATUS_IS_LOGINED = 2;
	public static final int STATUS_IS_DELETED = 99;

	/*
	 * 0-qq注册登录 1-微信注册登录 2-手机注册登录 3-苹果账号注册 4-一键登录 5-验证码注册登录 6-邮箱验证码登录
	 */
	public static final int REGISTER_TYPE_QQ = 0;
	public static final int REGISTER_TYPE_WX = 1;
	public static final int REGISTER_TYPE_PHONE = 2;
	public static final int REGISTER_TYPE_APPLE_USER = 3;
	public static final int REGISTER_TYPE_ALI_ONES = 4;
	public static final int REGISTER_TYPE_CAPTCHA = 5;
	public static final int REGISTER_TYPE_EMAIL_CAPTCHA = 6;

	/**
	 * @Title: removeSensitiveInfo
	 * @Description:
	 * @date 2019年3月13日 下午3:14:53
	 * <AUTHOR>
	 */
	public User removeSensitiveInfo() {
		return this.remove("userPass", "salt");
	}
}
