package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseUserBackup<M extends BaseUserBackup<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setUserData(java.lang.String userData) {
		set("userData", userData);
		return (M)this;
	}
	
	public java.lang.String getUserData() {
		return getStr("userData");
	}

	public M setReason(java.lang.String reason) {
		set("reason", reason);
		return (M)this;
	}
	
	public java.lang.String getReason() {
		return getStr("reason");
	}

	public M setIpAddress(java.lang.String ipAddress) {
		set("ipAddress", ipAddress);
		return (M)this;
	}
	
	public java.lang.String getIpAddress() {
		return getStr("ipAddress");
	}

	public M setBackupTime(java.util.Date backupTime) {
		set("backupTime", backupTime);
		return (M)this;
	}
	
	public java.util.Date getBackupTime() {
		return get("backupTime");
	}

	public M setExpireTime(java.util.Date expireTime) {
		set("expireTime", expireTime);
		return (M)this;
	}
	
	public java.util.Date getExpireTime() {
		return get("expireTime");
	}

	public M setIsDeleted(java.lang.Boolean isDeleted) {
		set("isDeleted", isDeleted);
		return (M)this;
	}
	
	public java.lang.Boolean getIsDeleted() {
		return get("isDeleted");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setUpdateTime(java.util.Date updateTime) {
		set("updateTime", updateTime);
		return (M)this;
	}
	
	public java.util.Date getUpdateTime() {
		return get("updateTime");
	}

}
