package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseLogoChildKind<M extends BaseLogoChildKind<M>> extends Model<M> implements IBean {

	public M setLogoChildKindId(java.lang.Integer logoChildKindId) {
		set("logoChildKindId", logoChildKindId);
		return (M)this;
	}
	
	public java.lang.Integer getLogoChildKindId() {
		return getInt("logoChildKindId");
	}

	public M setLogoChildKindName(java.lang.String logoChildKindName) {
		set("logoChildKindName", logoChildKindName);
		return (M)this;
	}
	
	public java.lang.String getLogoChildKindName() {
		return getStr("logoChildKindName");
	}

	public M setEnglishName(java.lang.String englishName) {
		set("englishName", englishName);
		return (M)this;
	}
	
	public java.lang.String getEnglishName() {
		return getStr("englishName");
	}

	public M setTraditionalName(java.lang.String traditionalName) {
		set("traditionalName", traditionalName);
		return (M)this;
	}
	
	public java.lang.String getTraditionalName() {
		return getStr("traditionalName");
	}

	public M setKoreanName(java.lang.String koreanName) {
		set("koreanName", koreanName);
		return (M)this;
	}
	
	public java.lang.String getKoreanName() {
		return getStr("koreanName");
	}

	public M setLogoKindId(java.lang.Integer logoKindId) {
		set("logoKindId", logoKindId);
		return (M)this;
	}
	
	public java.lang.Integer getLogoKindId() {
		return getInt("logoKindId");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setVersion(java.lang.Integer version) {
		set("version", version);
		return (M)this;
	}
	
	public java.lang.Integer getVersion() {
		return getInt("version");
	}

}
