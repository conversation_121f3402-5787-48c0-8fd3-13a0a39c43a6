package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseTempletGroupI18n<M extends BaseTempletGroupI18n<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setLocale(java.lang.String locale) {
		set("locale", locale);
		return (M)this;
	}
	
	public java.lang.String getLocale() {
		return getStr("locale");
	}

	public M setGroupId(java.lang.Integer groupId) {
		set("groupId", groupId);
		return (M)this;
	}
	
	public java.lang.Integer getGroupId() {
		return getInt("groupId");
	}

	public M setGroupName(java.lang.String groupName) {
		set("groupName", groupName);
		return (M)this;
	}
	
	public java.lang.String getGroupName() {
		return getStr("groupName");
	}

}
