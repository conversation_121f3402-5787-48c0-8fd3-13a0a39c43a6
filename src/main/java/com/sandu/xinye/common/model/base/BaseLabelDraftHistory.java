package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseLabelDraftHistory<M extends BaseLabelDraftHistory<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setPrinter(java.lang.String printer) {
		set("printer", printer);
		return (M)this;
	}
	
	public java.lang.String getPrinter() {
		return getStr("printer");
	}

	public M setPaperName(java.lang.String paperName) {
		set("paperName", paperName);
		return (M)this;
	}
	
	public java.lang.String getPaperName() {
		return getStr("paperName");
	}

	public M setPlatform(java.lang.String platform) {
		set("platform", platform);
		return (M)this;
	}
	
	public java.lang.String getPlatform() {
		return getStr("platform");
	}

	public M setDataType(java.lang.String dataType) {
		set("dataType", dataType);
		return (M)this;
	}
	
	public java.lang.String getDataType() {
		return getStr("dataType");
	}

	public M setFunType(java.lang.String funType) {
		set("funType", funType);
		return (M)this;
	}
	
	public java.lang.String getFunType() {
		return getStr("funType");
	}

	public M setPreviewUrl(java.lang.String previewUrl) {
		set("previewUrl", previewUrl);
		return (M)this;
	}
	
	public java.lang.String getPreviewUrl() {
		return getStr("previewUrl");
	}

	public M setData(java.lang.String data) {
		set("data", data);
		return (M)this;
	}
	
	public java.lang.String getData() {
		return getStr("data");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setUpdateTime(java.util.Date updateTime) {
		set("updateTime", updateTime);
		return (M)this;
	}
	
	public java.util.Date getUpdateTime() {
		return get("updateTime");
	}

}
