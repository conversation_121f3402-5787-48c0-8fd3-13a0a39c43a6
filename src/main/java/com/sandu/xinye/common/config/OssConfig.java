package com.sandu.xinye.common.config;

import com.jfinal.kit.PropKit;

/**
 * OSS相关配置管理类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class OssConfig {
    
    /**
     * 批量上传图片最大数量（默认10张）
     */
    public static int getBatchUploadMaxCount() {
        return PropKit.getInt("oss.batch.upload.maxCount", 10);
    }
    
    /**
     * 单个图片文件最大大小（MB，默认10MB）
     */
    public static int getSingleUploadMaxSizeMB() {
        return PropKit.getInt("oss.single.upload.maxSizeMB", 10);
    }
    
    /**
     * 单个图片文件最大大小（字节）
     */
    public static long getSingleUploadMaxSizeBytes() {
        return getSingleUploadMaxSizeMB() * 1024L * 1024L;
    }
    
    /**
     * 验证批量上传数量是否超限
     *
     * @param count 文件数量
     * @return true表示超限，false表示正常
     */
    public static boolean isBatchCountExceeded(int count) {
        return count > getBatchUploadMaxCount();
    }

    /**
     * 验证单个文件大小是否超限
     *
     * @param fileSizeBytes 文件大小（字节）
     * @return true表示超限，false表示正常
     */
    public static boolean isSingleFileSizeExceeded(long fileSizeBytes) {
        return fileSizeBytes > getSingleUploadMaxSizeBytes();
    }

    // ==================== 国际化消息Key常量 ====================

    /**
     * OSS上传相关的国际化消息Key
     */
    public static class MessageKeys {
        // 基础错误信息
        public static final String UPLOAD_FILE_EMPTY = "上传图片不能为空";
        public static final String USER_ID_EMPTY = "用户ID不能为空";
        public static final String UPLOAD_FAILED = "上传失败，请稍后重试";

        // 文件验证错误
        public static final String FILE_TYPE_INVALID = "文件类型不正确，只支持jpg、jpeg、png、bmp";
        public static final String FILE_SIZE_EXCEEDED = "文件过大，最大支持{0}MB";
        public static final String FILE_CONTENT_INVALID = "文件内容不是有效图片";

        // 批量上传错误
        public static final String BATCH_COUNT_EXCEEDED = "批量上传最多支持{0}张图片，当前选择了{1}张";
        public static final String BATCH_COUNT_EXCEEDED_STRICT = "批量上传最多支持{0}张图片，当前选择了{1}张，请重新选择";

        // 批量上传成功信息
        public static final String ALL_FILES_SUCCESS = "所有文件上传成功";
        public static final String ALL_FILES_FAILED = "所有文件上传失败";
        public static final String PARTIAL_FILES_SUCCESS = "部分文件上传成功，成功: {0}个，失败: {1}个";
        public static final String PARTIAL_FILES_SUCCESS_WITH_LIMIT = "部分文件上传成功，成功: {0}个，失败: {1}个（超出数量限制，只处理了前{2}张）";
        public static final String SUCCESS_WITH_LIMIT = "上传成功 {0} 张图片（超出数量限制，只处理了前{1}张）";

        // 警告信息
        public static final String BATCH_COUNT_LIMIT_WARNING = "上传文件数量超出限制（最多{0}张），只处理了前{0}张图片";

        // 配置信息
        public static final String BATCH_UPLOAD_CONFIG_INFO = "批量上传最多支持{0}张图片，单张图片最大{1}MB";
    }
}
