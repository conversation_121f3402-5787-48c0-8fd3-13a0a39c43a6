package com.sandu.xinye.common.config;

import com.jfinal.kit.PropKit;

/**
 * OSS相关配置管理类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class OssConfig {
    
    /**
     * 批量上传图片最大数量（默认10张）
     */
    public static int getBatchUploadMaxCount() {
        return PropKit.getInt("oss.batch.upload.maxCount", 10);
    }
    
    /**
     * 单个图片文件最大大小（MB，默认10MB）
     */
    public static int getSingleUploadMaxSizeMB() {
        return PropKit.getInt("oss.single.upload.maxSizeMB", 10);
    }
    
    /**
     * 单个图片文件最大大小（字节）
     */
    public static long getSingleUploadMaxSizeBytes() {
        return getSingleUploadMaxSizeMB() * 1024L * 1024L;
    }
    
    /**
     * 获取批量上传配置信息（用于日志和错误提示）
     */
    public static String getBatchUploadConfigInfo() {
        return String.format("批量上传最多支持%d张图片，单张图片最大%dMB", 
            getBatchUploadMaxCount(), getSingleUploadMaxSizeMB());
    }
    
    /**
     * 验证批量上传数量是否超限
     * 
     * @param count 文件数量
     * @return true表示超限，false表示正常
     */
    public static boolean isBatchCountExceeded(int count) {
        return count > getBatchUploadMaxCount();
    }
    
    /**
     * 验证单个文件大小是否超限
     * 
     * @param fileSizeBytes 文件大小（字节）
     * @return true表示超限，false表示正常
     */
    public static boolean isSingleFileSizeExceeded(long fileSizeBytes) {
        return fileSizeBytes > getSingleUploadMaxSizeBytes();
    }
    
    /**
     * 获取批量上传数量超限的错误信息
     * 
     * @param actualCount 实际文件数量
     * @return 错误信息
     */
    public static String getBatchCountExceededMessage(int actualCount) {
        return String.format("批量上传最多支持%d张图片，当前选择了%d张", 
            getBatchUploadMaxCount(), actualCount);
    }
    
    /**
     * 获取文件大小超限的错误信息
     * 
     * @return 错误信息
     */
    public static String getFileSizeExceededMessage() {
        return String.format("文件过大，最大支持%dMB", getSingleUploadMaxSizeMB());
    }
    
    /**
     * 获取批量上传数量限制警告信息（用于宽松模式）
     * 
     * @return 警告信息
     */
    public static String getBatchCountLimitWarning() {
        return String.format("上传文件数量超出限制（最多%d张），只处理了前%d张图片", 
            getBatchUploadMaxCount(), getBatchUploadMaxCount());
    }
}
