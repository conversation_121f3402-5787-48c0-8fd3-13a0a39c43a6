package com.sandu.xinye.common.interceptor;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.Ret;

/**
 * 指定方法必须是 POST
 *
 * <AUTHOR>
 * @date 2024/3/14
 */
public class PostOnlyInterceptor implements Interceptor {

  @Override
  public void intercept(Invocation inv) {
    Controller controller = inv.getController();
    if (!controller.getRequest().getMethod().equalsIgnoreCase("POST")) {
      controller.renderJson(Ret.fail("message", "Method Not Allowed").set("status", 405));
    } else {
      inv.invoke();
    }
  }
}
