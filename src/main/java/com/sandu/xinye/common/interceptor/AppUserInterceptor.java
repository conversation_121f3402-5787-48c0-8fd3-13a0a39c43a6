package com.sandu.xinye.common.interceptor;

import javax.servlet.http.HttpServletRequest;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.i18n.I18n;
import com.jfinal.i18n.Res;
import com.jfinal.kit.StrKit;
import com.jfinal.render.JsonRender;
import com.jfinal.render.Render;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.api.login.UserLoginService;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import org.apache.log4j.Logger;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import com.jfinal.kit.PropKit;
import java.util.Locale;

public class AppUserInterceptor implements Interceptor {
    private static final Logger logger = Logger.getLogger(AppUserInterceptor.class);
    private static final Map<String, String> LOCALE_MAP = new HashMap<>();

    // 常量定义
    private static final String HEADER_PLATFORM = "platform";
    private static final String HEADER_ACCEPT_LANGUAGE = "Accept-Language";
    private static final String HEADER_MSG = "msg";
    private static final String HEADER_SUCCESS = "success";
    private static final String DEFAULT_LOCALE = "zh_CN";

    static {
        LOCALE_MAP.put("en-US", String.valueOf(Locale.US));
        LOCALE_MAP.put("zh-CN", String.valueOf(Locale.SIMPLIFIED_CHINESE));
        LOCALE_MAP.put("zh-HK", "zh_HK");
        LOCALE_MAP.put("ko-KR", String.valueOf(Locale.KOREA));
        LOCALE_MAP.put("ja-JP", String.valueOf(Locale.JAPAN));
        // 法语
        LOCALE_MAP.put("fr-FR", String.valueOf(Locale.FRANCE));
        // 德语
        LOCALE_MAP.put("de-DE", String.valueOf(Locale.GERMANY));
        // 意大利语
        LOCALE_MAP.put("it-IT", String.valueOf(new Locale("it", "IT")));
        // 葡萄牙语
        LOCALE_MAP.put("pt-PT", String.valueOf(new Locale("pt", "PT")));
        // 西班牙语
        LOCALE_MAP.put("es-ES", String.valueOf(new Locale("es", "ES")));
        // 俄语
        LOCALE_MAP.put("ru-RU", String.valueOf(new Locale("ru", "RU")));
        // 泰国
        LOCALE_MAP.put("th-TH", String.valueOf(new Locale("th", "TH")));
        // 越南语
        LOCALE_MAP.put("vi-VN", String.valueOf(new Locale("vi", "VN")));
    }

    @Override
    public void intercept(Invocation inv) {
        Method method = inv.getMethod();
        Controller con = inv.getController();
        con.getClass().getDeclaredMethods();
        String sessionId = con.getHeader(Constant.APP_ACCESSTOKE);
        String platform = con.getHeader(HEADER_PLATFORM);

        if (StrKit.isBlank(sessionId)) {
            con.renderJson(RetKit.fail(RetConstant.CODE_NO_LOGIN, "未登录！"));
            return;
        }
        if (StrKit.isBlank(platform)) {
            con.renderJson(RetKit.fail(RetConstant.CODE_NO_LOGIN, "用户platform不存在，请检查参数！"));
            return;
        }
        User user = UserLoginService.me.getUserCacheWithSessionId(sessionId);
        if (user == null) {
            user = UserLoginService.me.loginWithSessionId(sessionId, getIpAddress(con.getRequest()),
                    Integer.valueOf(platform));
        }
        if (user == null) {
            con.renderJson(RetKit.fail(RetConstant.CODE_LOGIN_EXPIRE, "Session已过期，请重新登录！"));
            return;
        }

        String localeHeader = con.getHeader(HEADER_ACCEPT_LANGUAGE);
        String locale = null;
        if (localeHeader != null) {
            locale = localeHeader.split(",")[0];
            if (LOCALE_MAP.containsKey(locale)) {
                locale = LOCALE_MAP.get(locale);
            } else {
                locale = PropKit.get("i18n.defaultLocale", DEFAULT_LOCALE);
            }
        } else {
            locale = PropKit.get("i18n.defaultLocale", DEFAULT_LOCALE);
        }

        inv.invoke();

        // 操作日志记录，增加详细日志
        boolean hasAnnotation = method.isAnnotationPresent(OperationLog.class);
        if (hasAnnotation) {
            OperationLog logBean = method.getAnnotation(OperationLog.class);
            String logContent = String.format("用户 %s 调用了接口 %s/%s", user.getUserNickName(), con.getControllerKey(),
                    method.getName());

            Render r = inv.getController().getRender();
            if (r instanceof JsonRender) {
                JSONObject ret = JSONUtil.parseObj(((JsonRender) r).getJsonText());
                if (ret.containsKey(HEADER_SUCCESS) && Boolean.TRUE.equals(ret.getBool(HEADER_SUCCESS))) {
                    OperationLogService.me.saveOperationLog(user.getUserId(), getIpAddress(con.getRequest()),
                            logBean.modelName(), logContent);
                    logger.info("操作日志已记录: " + logContent);
                }
            }
        }

        // i18n 国际化处理
        Render r = inv.getController().getRender();
        if (r instanceof JsonRender) {
            // 避免重复渲染
            if (!con.getResponse().isCommitted()) {
                String JsonText = ((JsonRender) r).getJsonText();
                JSONObject jsonObject = JSONUtil.parseObj(JsonText);
                try {
                    if (StrKit.isBlank(locale)) {
                        locale = DEFAULT_LOCALE;
                    }
                    Res resLocale = I18n.use(locale);
                    if (resLocale == null) {
                        logger.warn("未找到对应的国际化资源: " + locale);
                        return;
                    }
                    String msg = jsonObject.getStr(HEADER_MSG);
                    if (StrKit.notBlank(msg)) {
                        String localeMsg = resLocale.get(msg);
                        if (StrKit.notBlank(localeMsg)) {
                            jsonObject.put(HEADER_MSG, localeMsg);
                            con.renderText(JSONUtil.toJsonStr(jsonObject));
                            logger.info("国际化消息替换成功: " + msg + " -> " + localeMsg);
                        } else {
                            logger.info("国际化资源未找到对应msg: " + msg);
                        }
                    } else {
                        logger.info("返回JSON中未包含msg字段，无需国际化处理");
                    }
                } catch (Exception e) {
                    logger.error("国际化处理异常: " + e.getMessage(), e);
                }
            } else {
                logger.warn("响应已提交，无法进行国际化渲染");
            }
        }
    }

    /**
     * 获取客户端真实IP地址，支持多种代理头，循环优化
     */
    private String getIpAddress(HttpServletRequest request) {
        String[] headers = {
                "X-requested-For",
                "X-Forwarded-For",
                "Proxy-Client-IP",
                "WL-Proxy-Client-IP",
                "HTTP_CLIENT_IP",
                "HTTP_X_FORWARDED_FOR"
        };
        String ip = null;
        for (String header : headers) {
            ip = request.getHeader(header);
            if (StrKit.notBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                break;
            }
        }
        if (StrKit.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 多IP处理
        if (ip != null && ip.contains(",")) {
            String[] ips = ip.split(",");
            for (String strIp : ips) {
                if (!"unknown".equalsIgnoreCase(strIp.trim())) {
                    ip = strIp.trim();
                    break;
                }
            }
        }
        return ip;
    }

}
