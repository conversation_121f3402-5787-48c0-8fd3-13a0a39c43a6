package com.sandu.xinye.common.interceptor;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.annotation.RateLimit;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.IpKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.IpFilter;
import com.sandu.xinye.common.model.User;
import org.apache.log4j.Logger;

import java.lang.reflect.Method;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 混合限流拦截器
 * 同时支持IP级限流（宽松，适应共享IP）和用户级限流（严格，防止个人滥用）
 *
 * <AUTHOR>
 */
public class HybridRateLimitInterceptor implements Interceptor {
  private static final Logger logger = Logger.getLogger(HybridRateLimitInterceptor.class);

  /**
   * IP级限流：每分钟默认最大访问次数（适应共享IP场景）
   */
  private static final int DEFAULT_IP_MAX_ACCESS_PER_MINUTE = 50;

  /**
   * 用户级限流：每分钟默认最大访问次数（防止个人滥用）
   */
  private static final int DEFAULT_USER_MAX_ACCESS_PER_MINUTE = 10;

  /**
   * IP级限流缓存，每分钟自动清理
   */
  private static LoadingCache<String, AtomicLong> ipLimitCache = CacheBuilder.newBuilder()
      .maximumSize(2000L)
      .expireAfterWrite(1, TimeUnit.MINUTES)
      .build(new CacheLoader<String, AtomicLong>() {
        @Override
        public AtomicLong load(String key) {
          return new AtomicLong(0);
        }
      });

  /**
   * 用户级限流缓存，每分钟自动清理
   */
  private static LoadingCache<String, AtomicLong> userLimitCache = CacheBuilder.newBuilder()
      .maximumSize(10000L)
      .expireAfterWrite(1, TimeUnit.MINUTES)
      .build(new CacheLoader<String, AtomicLong>() {
        @Override
        public AtomicLong load(String key) {
          return new AtomicLong(0);
        }
      });

  // 缓存白名单，30分钟刷新一次
  private static LoadingCache<String, Boolean> whiteListCache = CacheBuilder.newBuilder()
      .expireAfterWrite(30, TimeUnit.MINUTES)
      .build(new CacheLoader<String, Boolean>() {
        @Override
        public Boolean load(String ip) {
          IpFilter filter = IpFilter.dao.findFirst("select * from ip_filter where ip = ? and type = 0", ip);
          return filter != null;
        }
      });

  // 缓存黑名单，5分钟刷新一次
  private static LoadingCache<String, Boolean> blackListCache = CacheBuilder.newBuilder()
      .expireAfterWrite(5, TimeUnit.MINUTES)
      .build(new CacheLoader<String, Boolean>() {
        @Override
        public Boolean load(String ip) {
          IpFilter filter = IpFilter.dao.findFirst("select * from ip_filter where ip = ? and type = 1", ip);
          return filter != null;
        }
      });

  @Override
  public void intercept(Invocation inv) {
    Controller con = inv.getController();
    String ip = IpKit.getRealIp(con.getRequest());
    if (StrKit.isBlank(ip)) {
      con.renderJson(RetKit.fail(400, "参数错误！"));
      return;
    }

    try {
      // 检查黑白名单
      Boolean inWhiteList = isInWhiteList(ip);
      Boolean inBlackList = isInBlackList(ip);
      if (inBlackList) {
        con.renderJson(RetKit.fail(RetConstant.CODE_REQUEST_TOO_FAST, "请求受限，请联系管理员！"));
        return;
      }

      // 白名单IP跳过所有限流检查
      if (inWhiteList) {
        logger.debug("白名单IP[" + ip + "]跳过限流检查");
        inv.invoke();
        return;
      }

      // 获取限流配置
      int userMaxAccess = getUserMaxAccessPerMinute(inv);
      int ipMaxAccess = getIpMaxAccessPerMinute(inv);

      // IP级限流检查（始终执行）
      String ipKey = ip + ":" + inv.getActionKey();
      AtomicLong ipLimiter = ipLimitCache.get(ipKey);
      if (ipLimiter.incrementAndGet() > ipMaxAccess) {
        logger.info(String.format("IP[%s]请求太频繁，超出每分钟限制次数: %s，接口：%s", ip, ipMaxAccess, inv.getActionKey()));
        con.renderJson(RetKit.fail(RetConstant.CODE_REQUEST_TOO_FAST, "IP请求过于频繁，请稍后再试！"));
        return;
      }

      // 用户级限流检查（仅对已登录用户执行）
      String userId = getCurrentUserId(con);
      if (userId != null) {
        String userKey = userId + ":" + inv.getActionKey();
        AtomicLong userLimiter = userLimitCache.get(userKey);
        if (userLimiter.incrementAndGet() > userMaxAccess) {
          logger.info(String.format("用户[%s]请求太频繁，超出每分钟限制次数: %s，接口：%s", userId, userMaxAccess, inv.getActionKey()));
          con.renderJson(RetKit.fail(RetConstant.CODE_REQUEST_TOO_FAST, "个人操作太频繁，请稍后再试！"));
          return;
        }
        logger.debug(String.format("用户[%s]限流检查通过，当前计数: %s/%s", userId, userLimiter.get(), userMaxAccess));
      } else {
        // 未登录用户只受IP限流保护
        logger.debug(String.format("未登录用户访问接口[%s]，仅执行IP级限流检查", inv.getActionKey()));
      }

      logger.debug(String.format("IP[%s]限流检查通过，当前计数: %s/%s", ip, ipLimiter.get(), ipMaxAccess));

    } catch (ExecutionException e) {
      logger.error("限流检查异常：" + e.getMessage(), e);
      con.renderJson(RetKit.fail(400, "参数错误，接口请求失败！"));
      return;
    } catch (Exception e) {
      logger.error("限流检查系统错误：" + e.getMessage(), e);
      con.renderJson(RetKit.fail(500, "系统内部错误！"));
      return;
    }

    inv.invoke();
  }

  /**
   * 获取当前用户ID
   */
  private String getCurrentUserId(Controller con) {
    try {
      if (con instanceof AppController) {
        AppController appController = (AppController) con;
        User user = appController.getUser();
        return user != null ? user.getUserId().toString() : null;
      }
    } catch (Exception e) {
      logger.debug("获取用户ID失败：" + e.getMessage());
    }
    return null;
  }

  /**
   * 获取用户级限流次数
   * 优先从方法注解获取，其次从类注解获取，最后使用默认值
   */
  private int getUserMaxAccessPerMinute(Invocation inv) {
    Method method = inv.getMethod();
    // 优先获取方法上的注解
    RateLimit methodAnnotation = method.getAnnotation(RateLimit.class);
    if (methodAnnotation != null) {
      return methodAnnotation.value();
    }

    // 其次获取类上的注解
    Class<?> controllerClass = inv.getController().getClass();
    RateLimit classAnnotation = controllerClass.getAnnotation(RateLimit.class);
    if (classAnnotation != null) {
      return classAnnotation.value();
    }

    // 最后使用默认值
    return DEFAULT_USER_MAX_ACCESS_PER_MINUTE;
  }

  /**
   * 获取IP级限流次数
   */
  private int getIpMaxAccessPerMinute(Invocation inv) {
    Method method = inv.getMethod();
    // 优先获取方法上的注解
    RateLimit methodAnnotation = method.getAnnotation(RateLimit.class);
    if (methodAnnotation != null && methodAnnotation.ipLimit() != -1) {
      return methodAnnotation.ipLimit();
    }

    // 其次获取类上的注解
    Class<?> controllerClass = inv.getController().getClass();
    RateLimit classAnnotation = controllerClass.getAnnotation(RateLimit.class);
    if (classAnnotation != null && classAnnotation.ipLimit() != -1) {
      return classAnnotation.ipLimit();
    }

    // 如果没有显式设置IP限流，则使用用户限流的5倍，最少50次
    int userLimit = getUserMaxAccessPerMinute(inv);
    return Math.max(userLimit * 5, DEFAULT_IP_MAX_ACCESS_PER_MINUTE);
  }

  private Boolean isInWhiteList(String ip) {
    try {
      return whiteListCache.get(ip);
    } catch (Exception e) {
      return false;
    }
  }

  private Boolean isInBlackList(String ip) {
    try {
      return blackListCache.get(ip);
    } catch (Exception e) {
      return false;
    }
  }
}