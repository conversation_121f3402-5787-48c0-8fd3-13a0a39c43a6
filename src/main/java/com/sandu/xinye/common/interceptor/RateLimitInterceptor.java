package com.sandu.xinye.common.interceptor;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.annotation.RateLimit;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.IpKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.IpFilter;
import org.apache.log4j.Logger;

import java.lang.reflect.Method;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 防止攻击
 * 同一IP限流
 *
 * <AUTHOR>
 */
public class RateLimitInterceptor implements Interceptor {
    private static final Logger logger = Logger.getLogger(AttackAntiInterceptor.class);

    /**
     * 默认每分钟最大访问次数
     */
    private static Integer DEFAULT_MAX_ACCESS_PER_MINUTE = 10;
    Prop p = PropKit.use("common_config.txt");

    {
        String fileName = p.get("sqlConfig");
        p.append(fileName);
        DEFAULT_MAX_ACCESS_PER_MINUTE = p.getInt("maxAccessPerMinute", DEFAULT_MAX_ACCESS_PER_MINUTE);
    }

    /**
     * 根据IP分不同的令牌桶, 每分钟自动清理缓存
     */
    private static LoadingCache<String, AtomicLong> cachesForMinute = CacheBuilder.newBuilder()
            .maximumSize(1000L)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, AtomicLong>() {
                @Override
                public AtomicLong load(String key) {
                    return new AtomicLong(0);
                }
            });

    // 缓存白名单，30分钟刷新一次
    private static LoadingCache<String, Boolean> whiteListCache = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Boolean>() {
                @Override
                public Boolean load(String ip) {
                    IpFilter filter = IpFilter.dao.findFirst("select * from ip_filter where ip = ? and type = 0", ip);
                    return filter != null;
                }
            });

    // 缓存黑名单，5分钟刷新一次
    private static LoadingCache<String, Boolean> blackListCache = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Boolean>() {
                @Override
                public Boolean load(String ip) {
                    IpFilter filter = IpFilter.dao.findFirst("select * from ip_filter where ip = ? and type = 1", ip);
                    return filter != null;
                }
            });

    @Override
    public void intercept(Invocation inv) {
        Controller con = inv.getController();
        String ip = IpKit.getRealIp(con.getRequest());
        if (StrKit.isBlank(ip)) {
            con.renderJson(RetKit.fail(400, "参数错误！"));
            return;
        }

        try {
            // 获取访问频率限制
            int maxAccessPerMinute = getMaxAccessPerMinute(inv);

            // 检查每分钟的限流
            String cacheKey = ip + ":" + inv.getActionKey(); // 加入接口标识，针对每个接口单独限流
            AtomicLong limiter = cachesForMinute.get(cacheKey);

            // IP是否在白名单中
            Boolean inWhiteList = isInWhiteList(ip);
            Boolean inBlackList = isInBlackList(ip);
            if (inBlackList) {
                con.renderJson(RetKit.fail(RetConstant.CODE_REQUEST_TOO_FAST, "请求受限，请联系管理员！"));
                return;
            }
            if (!inWhiteList && limiter.incrementAndGet() > maxAccessPerMinute) {
                logger.info(
                        String.format("当前IP[%s]请求太频繁，超出每分钟限制次数: %s，接口：%s", ip, maxAccessPerMinute, inv.getActionKey()));
                con.renderJson(RetKit.fail(RetConstant.CODE_REQUEST_TOO_FAST, "请求太频繁，请稍后再试！"));
                return;
            }

        } catch (ExecutionException e) {
            con.renderJson(RetKit.fail(400, "参数错误，接口请求失败！"));
            return;
        } catch (Exception e) {
            e.printStackTrace();
            con.renderJson(RetKit.fail(500, "系统内部错误！"));
            return;
        }

        inv.invoke();
    }

    /**
     * 获取访问频率限制
     * 优先从方法注解获取，其次从类注解获取，最后使用默认值
     */
    private int getMaxAccessPerMinute(Invocation inv) {
        Method method = inv.getMethod();
        // 优先获取方法上的注解
        RateLimit methodAnnotation = method.getAnnotation(RateLimit.class);
        if (methodAnnotation != null) {
            return methodAnnotation.value();
        }

        // 其次获取类上的注解
        Class<?> controllerClass = inv.getController().getClass();
        RateLimit classAnnotation = controllerClass.getAnnotation(RateLimit.class);
        if (classAnnotation != null) {
            return classAnnotation.value();
        }

        // 最后使用默认值
        return DEFAULT_MAX_ACCESS_PER_MINUTE;
    }

    private Boolean isInWhiteList(String ip) {
        try {
            return whiteListCache.get(ip);
        } catch (Exception e) {
            return false;
        }
    }

    private Boolean isInBlackList(String ip) {
        try {
            return blackListCache.get(ip);
        } catch (Exception e) {
            return false;
        }
    }
}
