package com.sandu.xinye.common.kit.oss;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.PutObjectRequest;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.constant.OssConstant;
import com.xiaoleilu.hutool.date.DateUtil;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
/**
 * 阿里云OSS工具类
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
public class AliOssV3Kit {
    private static final Logger logger = LoggerFactory.getLogger(AliOssV3Kit.class);

    /**
     * 上传文件
     * @param objectPrefix
     * @param file
     * @return
     * @throws Exception
     */
    public static String upload(String objectPrefix, UploadFile file) {
        List<UploadFile> files = new ArrayList<>();
        files.add(file);

        List<String> uploadPaths= upload(objectPrefix, files);
        if(!CollectionUtil.isEmpty(uploadPaths)){
            return uploadPaths.get(0);
        }

        return null;
    }

    /**
     * 修改文件
     * @param ossUrl
     * @param file
     * @return
     * @throws Exception
     */
    public static String update(String ossUrl, UploadFile file) {
        if (file != null) {
            try{
                // 获取
                String objectName = getOssName(ossUrl);
                putObject(objectName, file);
            }catch (Exception e){
                logger.error("oss上传错误：" + e.getMessage());
            }
        }

        return ossUrl;
    }


    /**
     * 上传文件
     * @param objectPrefix
     * @param files
     * @return
     * @throws Exception
     */
    public static List<String> upload(String objectPrefix, List<UploadFile> files) {
        //定义一个map用于接收图片信息
        List<String> uploadPaths = new ArrayList();
        objectPrefix = objectPrefix.trim();

        //循环遍历
        for (int i = 0; i < files.size(); i++) {
            UploadFile file = files.get(i);
            if (file != null) {
                try{
                    // 随机生成文件名（时间戳）
                    String fileName = Long.toString(DateUtil.current(false))  + '.' + FilenameUtils.getExtension(file.getOriginalFileName());
                    String objectName = !StrUtil.isEmpty(objectPrefix) ? objectPrefix + '/' + fileName : fileName;
                    logger.info("oss上传文件：" + objectName + ";prefix " + objectPrefix);
                    String ossName = putObject(objectName, file);
                    uploadPaths.add(ossName);
                }catch (Exception e){
                    logger.error("oss上传错误：" + e.getMessage());
                    break;
                }
            }
        }

        return uploadPaths;
    }

    private static String putObject(String objectName, UploadFile file) throws Exception {
        String ossName = objectName;
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(OssConstant.ENDPOINT, OssConstant.ACCESSKEY_ID, OssConstant.ACCESSKEY_SECRET);

        try {

            InputStream inputStream = new FileInputStream(file.getFile());
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(OssConstant.BUCKET_NAME, ossName, inputStream);
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 上传文件。
            ossClient.putObject(putObjectRequest);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
            throw oe;
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
            throw  ce;
        } catch (IOException ie) {
            System.out.println("Caught an IOException, which means your request file "
                    + "encountered a internal problem while trying to getInputStream for some reason.");
            System.out.println("Error Message:" + ie.getMessage());
            throw ie;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        return OssConstant.CDN_DOMAIN + ossName;
    }

    /**
     * 根据ossUrl获取文件名ossName
     */
    private static String getOssName(String ossUrl){
        return ossUrl.replace(OssConstant.CDN_DOMAIN, "");
    }

}
