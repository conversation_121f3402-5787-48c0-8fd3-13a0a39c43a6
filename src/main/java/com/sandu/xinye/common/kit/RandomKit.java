package com.sandu.xinye.common.kit;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 生成随机数字工具类
 * @date 2017年1月16日
 */
public class RandomKit {

    /**
     * @param @param  length
     * @param @return
     * @throws
     * @Description: 获取随机数字
     * @Title: getRandomString
     * <AUTHOR>
     * @date 2017年1月22日
     */
    public static String getRandomPsw(int length) { //length表示生成字符串的长度
        String base = "0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            if (i == 0 && number == 0) {    //首数字不为0
                number = 1;
            }
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * @param @param  length
     * @param @return
     * @throws
     * @Description: 获取随机数字和字母的组合
     * @Title: getRandomString
     * <AUTHOR>
     * @date 2022/10/28
     */
    public static String getRandomCharAndNum(int length) {
        String val = "";
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            // 输出字母还是数字
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 字符串
            if ("char".equalsIgnoreCase(charOrNum)) {
                //取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (choice + random.nextInt(26));
            } else if ("num".equalsIgnoreCase(charOrNum)) {
                // 数字
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;

    }

    public static String generateSixDigitCode() {
        // 生成100000（含）到1000000（不含）之间的随机整数
        int randomNum = ThreadLocalRandom.current().nextInt(100000, 1000000);
        return String.valueOf(randomNum);
    }

}
