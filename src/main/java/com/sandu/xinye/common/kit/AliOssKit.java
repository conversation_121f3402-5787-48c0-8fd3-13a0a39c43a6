package com.sandu.xinye.common.kit;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.PutObjectRequest;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.constant.OssConstant;
import com.xiaoleilu.hutool.date.DateUtil;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 阿里云OSS工具类
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
public class AliOssKit {
	private static final Logger logger = LoggerFactory.getLogger(AliOssKit.class);

	/**
	 * 上传文件
	 * 
	 * @param objectPrefix
	 * @param file
	 * @return
	 * @throws Exception
	 */
	public static String upload(String objectPrefix, UploadFile file) {
		List<UploadFile> files = new ArrayList<>();
		files.add(file);

		List<String> uploadPaths = upload(objectPrefix, files);
		if (!CollectionUtil.isEmpty(uploadPaths)) {
			return uploadPaths.get(0);
		}

		return null;
	}

	/**
	 * 上传文件
	 * 
	 * @param objectPrefix
	 * @param files
	 * @return
	 * @throws Exception
	 */
	public static List<String> upload(String objectPrefix, List<UploadFile> files) {
		// 定义一个map用于接收图片信息
		List<String> uploadPaths = new ArrayList();
		objectPrefix = objectPrefix.trim();

		// 循环遍历
		for (int i = 0; i < files.size(); i++) {
			UploadFile file = files.get(i);
			if (file != null) {
				try {
					// 随机生成文件名（时间戳）
					String fileName = UUID.randomUUID().toString().replace("-", "") + '.'
							+ FilenameUtils.getExtension(file.getOriginalFileName());
					String objectName = !StrUtil.isEmpty(objectPrefix) ? objectPrefix + '/' + fileName : fileName;
					logger.info("oss上传文件：" + objectName + ";prefix " + objectPrefix);
					String ossName = putObject(objectName, file);
					uploadPaths.add(ossName);
				} catch (Exception e) {
					logger.error("oss上传错误：" + e.getMessage());
					break;
				}
			}
		}

		return uploadPaths;
	}

	/**
	 * 上传文件，支持自定义文件名
	 * 
	 * @param objectPrefix
	 * @param file
	 * @param customFileName
	 * @return
	 */
	public static String upload(String objectPrefix, UploadFile file, String customFileName) {
		List<UploadFile> files = new ArrayList<>();
		files.add(file);
		List<String> uploadPaths = upload(objectPrefix, files, customFileName);
		if (!CollectionUtil.isEmpty(uploadPaths)) {
			return uploadPaths.get(0);
		}
		return null;
	}

	/**
	 * 上传文件，支持自定义文件名
	 * 
	 * @param objectPrefix
	 * @param files
	 * @param customFileName
	 * @return
	 */
	public static List<String> upload(String objectPrefix, List<UploadFile> files, String customFileName) {
		List<String> uploadPaths = new ArrayList();
		objectPrefix = objectPrefix.trim();
		for (int i = 0; i < files.size(); i++) {
			UploadFile file = files.get(i);
			if (file != null) {
				try {
					String fileName = customFileName;
					String objectName = !StrUtil.isEmpty(objectPrefix) ? objectPrefix + '/' + fileName : fileName;
					logger.info("oss上传文件：" + objectName + ";prefix " + objectPrefix);
					String ossName = putObject(objectName, file);
					uploadPaths.add(ossName);
				} catch (Exception e) {
					logger.error("oss上传错误");
					break;
				}
			}
		}
		return uploadPaths;
	}

	private static String putObject(String objectName, UploadFile file) throws Exception {
		String ossName = objectName;
		InputStream inputStream = null;
		OSS ossClient = new OSSClientBuilder().build(OssConstant.ENDPOINT, OssConstant.ACCESSKEY_ID,
				OssConstant.ACCESSKEY_SECRET);

		try {

			inputStream = new FileInputStream(file.getFile());
			// 创建PutObjectRequest对象。
			PutObjectRequest putObjectRequest = new PutObjectRequest(OssConstant.BUCKET_NAME, ossName, inputStream);
			// 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
			// ObjectMetadata metadata = new ObjectMetadata();
			// metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS,
			// StorageClass.Standard.toString());
			// metadata.setObjectAcl(CannedAccessControlList.Private);
			// putObjectRequest.setMetadata(metadata);

			// 上传文件。
			ossClient.putObject(putObjectRequest);
		} catch (OSSException oe) {
			logger.error("OSS服务返回错误: " + oe.getErrorCode() + ", 请求ID: " + oe.getRequestId());
			throw oe;
		} catch (ClientException ce) {
			logger.error("OSS客户端异常: " + ce.getMessage());
			throw ce;
		} catch (IOException ie) {
			logger.error("IO异常: " + ie.getMessage());
			throw ie;
		} finally {
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					logger.error("关闭输入流异常: " + e.getMessage());
				}
			}
			if (ossClient != null) {
				ossClient.shutdown();
			}
		}

		return OssConstant.CDN_DOMAIN + ossName;
	}

}
