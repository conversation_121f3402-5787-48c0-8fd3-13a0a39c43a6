package com.sandu.xinye.common.routes;

import com.jfinal.config.Routes;
import com.sandu.xinye.admin.auth.RoleController;
import com.sandu.xinye.admin.auth.SysUserController;
import com.sandu.xinye.admin.density.DensityController;
import com.sandu.xinye.admin.font.FontController;
import com.sandu.xinye.admin.help.HelpController;
import com.sandu.xinye.admin.login.LoginController;
import com.sandu.xinye.admin.material.MaterialController;
import com.sandu.xinye.admin.my.MyController;
import com.sandu.xinye.admin.operate.OperationLogController;
import com.sandu.xinye.admin.oss.OssController;
import com.sandu.xinye.admin.upload.UploadController;
import com.sandu.xinye.common.interceptor.SysAdminInterceptor;

public class AdminRoutes extends Routes {

    @Override
    public void config() {
        this.addInterceptor(new SysAdminInterceptor());

        // 登录
        this.add("/admin", LoginController.class);
        this.add("/admin/login", LoginController.class);
        //首页
        // 上传
        this.add("/admin/upload", UploadController.class);
        // oss
        this.add("/admin/oss", OssController.class);


        //权限
        this.add("/admin/auth/account", SysUserController.class);
        this.add("/admin/auth/role", RoleController.class);
        //操作日志
        this.add("/admin/operate", OperationLogController.class);
        //我的
        this.add("/admin/my", MyController.class);
        //素材管理
        this.add("/admin/material", MaterialController.class);
        //浓度配置
        this.add("/admin/density", DensityController.class);
        //教程配置
        this.add("/admin/help", HelpController.class);
        //字体管理
        this.add("/admin/font", FontController.class);
    }



}
