package com.sandu.xinye.common.routes;

import com.jfinal.config.Routes;
import com.sandu.xinye.api.app_version.AppVersionController;
import com.sandu.xinye.api.common.CommonController;
import com.sandu.xinye.api.density.DensityController;
import com.sandu.xinye.api.font.FontController;
import com.sandu.xinye.api.help.HelpController;
import com.sandu.xinye.api.label_draft_history.LabelDraftHistoryController;
import com.sandu.xinye.api.login.UserLoginController;
import com.sandu.xinye.api.material.MaterialController;
import com.sandu.xinye.api.oss.OssController;
import com.sandu.xinye.api.user.UserController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;

public class ApiRoutes extends Routes {

    @Override
    public void config() {
        this.addInterceptor(new AppUserInterceptor());

        // 通用
        this.add("/api/common", CommonController.class);
        // 登录
        this.add("/api/login", UserLoginController.class);
        // 用户
        this.add("/api/user", UserController.class);
        // 版本
        this.add("/api/version", AppVersionController.class);
        // 通用
        this.add("/api/material", MaterialController.class);
        // 通用
        this.add("/api/density", DensityController.class);

        this.add("/api/help", HelpController.class);

        // 字体接口
        this.add("/api/font", FontController.class);
        // 阿里云oss接口
        this.add("/api/oss", OssController.class);
        // 草稿/历史接口
        this.add("/api/labelDraftHistory", LabelDraftHistoryController.class);

    }

}
