package com.sandu.xinye.api.material;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.admin.model.GroupSort;
import com.sandu.xinye.admin.model.Materials;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.annotation.RateLimit;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.HybridRateLimitInterceptor;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Material;
import com.sandu.xinye.common.model.MaterialGroup;

/**
 * 素材接口控制器
 * 注意：素材接口允许匿名访问，但需要限流保护
 */
@Before({HybridRateLimitInterceptor.class, I18nInterceptor.class})
@RateLimit(ipLimit = 50)
@Clear
public class MaterialController extends AppController {

    /**
     * 获取素材分组列表
     * 添加缓存支持，减少数据库查询
     */
    public void getGroupList() {
        RetKit ret = MaterialService.me.getGroupList();
        renderJson(ret);
    }

    /**
     * 获取素材列表
     * 添加缓存支持和参数验证
     */
    public void list() {
        // 参数验证和限制
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);

        // 限制每页最大数量，防止大量数据请求
        if (pageSize > 50) {
            pageSize = 50;
        }
        if (pageNumber < 1) {
            pageNumber = 1;
        }

        RetKit ret = MaterialService.me.list(pageSize, pageNumber, getParaToMap());
        renderJson(ret);
    }
}
