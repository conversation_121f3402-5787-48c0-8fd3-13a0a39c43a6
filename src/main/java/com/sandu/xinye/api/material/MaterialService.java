package com.sandu.xinye.api.material;

import com.jfinal.kit.Kv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.sandu.xinye.common.model.Material;
import com.sandu.xinye.common.model.MaterialGroup;

import java.util.List;

public class MaterialService {
    public static final MaterialService me = new MaterialService();

    public RetKit list(int pageSize, int pageNumber, Kv kv) {
        // 生成缓存key
        String cacheKey = CacheConstant.MATERIAL_LIST + "_" + pageSize + "_" + pageNumber + "_" + kv.toString();

        // 尝试从缓存获取
        Page<Material> cachedPage = CacheKit.get(CacheConstant.MATERIAL, cacheKey);
        if (cachedPage != null) {
            return RetKit.ok("data", cachedPage);
        }

        String groupId = kv.getStr("groupId");
        Kv newKv = new Kv();
        if (groupId != null) {
            if (groupId.equals("-100")) {
                newKv.set("is_new", "1");
            } else if (!groupId.equals("-1")) {
                newKv.set("groupId", groupId);
            }
        }

        SqlPara sqlPara = Db.getSqlPara("admin.material.paginate", newKv);
        Page<Material> page = Material.dao.paginate(pageNumber, pageSize, sqlPara);

        // 遍历 Page 中的每个 Material 对象，添加缩略图
        for (Material material : page.getList()) {
            String originalUrl = material.getUrlPreview();
            if (originalUrl != null) {
                // 拼接缩略图参数到 url 上
                String thumbnailUrl = originalUrl + "?x-oss-process=image/resize,w_200";
                material.set("url_preview", thumbnailUrl);
            }
        }

        // 存入缓存
        CacheKit.put(CacheConstant.MATERIAL, cacheKey, page);

        return RetKit.ok("data", page);
    }

    public RetKit getGroupList() {
        // 尝试从缓存获取
        List<MaterialGroup> cachedList = CacheKit.get(CacheConstant.MATERIAL, CacheConstant.MATERIAL_GROUP_LIST);
        if (cachedList != null) {
            return RetKit.ok("list", cachedList);
        }

        String sql = "select * from material_group where status = 1 order by sort asc";
        List<MaterialGroup> list = MaterialGroup.dao.find(sql);

        // 存入缓存
        CacheKit.put(CacheConstant.MATERIAL, CacheConstant.MATERIAL_GROUP_LIST, list);

        return RetKit.ok("list", list);
    }

    /**
     * 清除素材相关缓存
     * 当素材数据发生变化时调用
     */
    public void clearCache() {
        CacheKit.removeAll(CacheConstant.MATERIAL);
    }
}
