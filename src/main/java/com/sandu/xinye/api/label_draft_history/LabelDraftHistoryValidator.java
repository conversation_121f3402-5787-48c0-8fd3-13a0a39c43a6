package com.sandu.xinye.api.label_draft_history;

import com.jfinal.core.Controller;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.validator.BaseValidator;

/**
 * 草稿/历史数据校验器
 */
public class LabelDraftHistoryValidator extends BaseValidator {

  @Override
  protected void validate(Controller c) {
    // 校验必填字段
    validateBodyParamNotNull("printer", "打印机名称");
    validateBodyParamNotNull("paperName", "纸张名称");
    validateBodyParamNotNull("dataType", "数据类型");
    validateBodyParamNotNull("funType", "功能类型");
  }

  @Override
  protected void handleError(Controller c) {
    // 获取当前ActionKey来区分不同的操作
    String actionKey = getActionKey();

    // 保存上一次提交的表单数据，避免用户重新输入
    c.keepPara();

    // 返回错误信息
    c.renderJson(RetKit.fail(c.getAttr(erroKey)));
  }
}
