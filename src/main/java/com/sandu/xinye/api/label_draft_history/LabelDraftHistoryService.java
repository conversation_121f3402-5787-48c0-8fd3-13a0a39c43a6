package com.sandu.xinye.api.label_draft_history;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.model.LabelDraftHistory;
import com.sandu.xinye.common.kit.RetKit;

import java.util.Date;
import java.util.List;

public class LabelDraftHistoryService {
  public static final LabelDraftHistoryService me = new LabelDraftHistoryService();

  // 保存草稿/历史数据（新增或更新）
  public RetKit save(LabelDraftHistory label) {
    Record record = new Record()
        .set("printer", label.getPrinter())
        .set("paperName", label.getPaperName())
        .set("platform", label.getPlatform())
        .set("dataType", label.getDataType())
        .set("funType", label.getFunType())
        .set("previewUrl", label.getPreviewUrl())
        .set("data", label.getData())
        .set("userId", label.getUserId());
    if (label.getId() != null && label.getId() > 0) {
      // 更新
      record.set("id", label.getId());
      boolean success = Db.update(LabelDraftHistory.TABLE_NAME, record);
      return success ? RetKit.ok("更新成功") : RetKit.fail("更新失败");
    } else {
      // 新增
      record.set("createTime", new Date());
      record.set("updateTime", new Date());
      boolean success = Db.save(LabelDraftHistory.TABLE_NAME, record);
      return success ? RetKit.ok("保存成功").set("data", record.getInt("id")) : RetKit.fail("保存失败");
    }
  }

  // 获取草稿/历史列表（分页，字段白名单，异常处理，不返回data字段）
  public List<Record> list(Long userId, String printer, String paperName, String dataType, String funType,
      int pageNumber,
      int pageSize) {
    StringBuilder sql = new StringBuilder("from label_draft_history where 1=1");
    List<Object> params = new java.util.ArrayList<>();
    sql.append(" and userId = ?");
    params.add(userId);
    if (printer != null && !printer.isEmpty()) {
      sql.append(" and printer = ?");
      params.add(printer);
    }
    if (paperName != null && !paperName.isEmpty()) {
      sql.append(" and paperName = ?");
      params.add(paperName);
    }
    if (dataType != null && !dataType.isEmpty()) {
      sql.append(" and dataType = ?");
      params.add(dataType);
    }
    if (funType != null && !funType.isEmpty()) {
      sql.append(" and funType = ?");
      params.add(funType);
    }
    sql.append(" order by updateTime desc");
    try {
      // 不返回data字段
      String select = "select id, printer, paperName, platform, dataType, funType, previewUrl, createTime, updateTime ";
      return Db.paginate(pageNumber, pageSize, select, sql.toString(), params.toArray()).getList();
    } catch (Exception e) {
      // Logger.error("查询草稿历史异常", e);
      return java.util.Collections.emptyList();
    }
  }

  // 新增：通过id查询详情，返回data字段
  public Record detail(int id) {
    if (id <= 0) {
      return null;
    }
    String sql = "select id, printer, paperName, platform, dataType, funType, previewUrl, data, userId, createTime, updateTime from label_draft_history where id = ?";
    try {
      return Db.findFirst(sql, id);
    } catch (Exception e) {
      // Logger.error("查询草稿历史详情异常", e);
      return null;
    }
  }

  // 删除草稿/历史（支持批量）
  public RetKit delete(Long userId, List<Integer> ids) {
    if (ids == null || ids.isEmpty()) {
      return RetKit.fail("参数不能为空");
    }
    String inSql = ids.toString().replace("[", "(").replace("]", ")");
    int count = Db.update("delete from label_draft_history where userId = ? and id in " + inSql, userId);
    return count > 0 ? RetKit.ok("删除成功") : RetKit.fail("删除失败");
  }
}