package com.sandu.xinye.api.label_draft_history;

import com.jfinal.aop.Before;
import com.sandu.xinye.common.annotation.OperationLog;
import com.jfinal.core.Controller;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.model.LabelDraftHistory;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.constant.RetConstant;
import com.alibaba.fastjson.JSON;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.interceptor.JsonBodyInterceptor;

import java.util.ArrayList;
import java.util.List;


public class LabelDraftHistoryController extends AppController {
  /**
   * 保存草稿/历史数据
   * POST /api/labelDraftHistory/save
   */
  @OperationLog(modelName = "labelDraftHistory")
  @Before({ PostOnlyInterceptor.class, LabelDraftHistoryValidator.class, JsonBodyInterceptor.class })
  public void save(@JsonBody LabelDraftHistory label) {
    if (getUser() == null) {
      renderJson(RetKit.fail(RetConstant.CODE_NO_LOGIN, "用户未登录"));
      return;
    }
    label.setUserId(getUser().getUserId());
    RetKit ret = LabelDraftHistoryService.me.save(label);
    renderJson(ret);
  }

  /**
   * 获取草稿/历史数据列表
   * GET
   * /api/labelDraftHistory/list?printer=xxx&paperName=xxx&dataType=xxx&funType=xxx
   */
  public void list() {
    if (getUser() == null) {
      renderJson(RetKit.fail(RetConstant.CODE_NO_LOGIN, "用户未登录"));
      return;
    }

    String printer = getPara("printer");
    String paperName = getPara("paperName");
    String dataType = getPara("dataType");
    String funType = getPara("funType");
    int pageNumber = getParaToInt("pageNumber", 1);
    int pageSize = getParaToInt("pageSize", 10);
    Long userId = getUser().getUserId().longValue();
    List<Record> list = LabelDraftHistoryService.me.list(userId, printer, paperName, dataType, funType, pageNumber,
        pageSize);
    renderJson(RetKit.ok().set("list", list));
  }

  /**
   * 获取草稿/历史数据详情
   * GET /api/labelDraftHistory/detail?id=xxx
   */
  public void detail() {
    if (getUser() == null) {
      renderJson(RetKit.fail(RetConstant.CODE_NO_LOGIN, "用户未登录"));
      return;
    }

    Integer id = getParaToInt("id");
    if (id == null || id <= 0) {
      renderJson(RetKit.fail("传参错误！"));
      return;
    }

    Record detail = LabelDraftHistoryService.me.detail(id);
    if (detail == null) {
      renderJson(RetKit.fail("数据不存在!"));
      return;
    }

    // 验证数据是否属于当前用户
    Long userId = getUser().getUserId().longValue();
    Long dataUserId = detail.getLong("userId");
    if (dataUserId == null || !dataUserId.equals(userId)) {
      renderJson(RetKit.fail("无权限访问!"));
      return;
    }

    renderJson(RetKit.ok().set("detail", detail));
  }

  /**
   * 删除草稿/历史数据（支持批量）
   * POST /api/labelDraftHistory/delete
   * 参数：ids（逗号分隔）
   */
  @OperationLog(modelName = "labelDraftHistory")
  @Before({ PostOnlyInterceptor.class })
  public void delete() {
    if (getUser() == null) {
      renderJson(RetKit.fail(RetConstant.CODE_NO_LOGIN, "用户未登录"));
      return;
    }

    Record bodyPara = getArgsRecord();
    String idsStr = bodyPara.getStr("ids");
    if (idsStr == null || idsStr.isEmpty()) {
      renderJson(RetKit.fail("参数不能为空"));
      return;
    }
    String[] idArr = idsStr.split(",");
    List<Integer> ids = new ArrayList<>();
    for (String id : idArr) {
      try {
        ids.add(Integer.parseInt(id));
      } catch (NumberFormatException ignored) {
      }
    }
    Long userId = getUser().getUserId().longValue();
    RetKit ret = LabelDraftHistoryService.me.delete(userId, ids);
    renderJson(ret);
  }
}