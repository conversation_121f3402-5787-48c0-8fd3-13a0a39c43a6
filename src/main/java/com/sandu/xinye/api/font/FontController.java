package com.sandu.xinye.api.font;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.common.annotation.RateLimit;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.HybridRateLimitInterceptor;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.kit.RetKit;

/**
 * 字体接口控制器
 * 注意：字体接口允许匿名访问，但需要限流保护
 */
@Before({ HybridRateLimitInterceptor.class, I18nInterceptor.class })
@RateLimit(ipLimit = 50)
@Clear
public class FontController extends AppController {

	/**
	 * 获取字体列表
	 * 添加缓存支持和参数验证
	 */
	public void list() {
		// 参数验证和限制
		int pageNumber = getParaToInt("pageNumber", 1);
		int pageSize = getParaToInt("pageSize", 10);

		// 限制每页最大数量，防止大量数据请求
		if (pageSize > 50) {
			pageSize = 50;
		}
		if (pageNumber < 1) {
			pageNumber = 1;
		}

		RetKit ret = FontService.me.list(pageSize, pageNumber, getParaToMap());
		renderJson(ret);
	}

	/**
	 * 获取字体本地化列表
	 * 添加缓存支持和参数验证
	 */
	public void locale() {
		String i18n = getPara("i18n", "zh-CN");

		// 参数验证：只允许特定的语言代码
		if (!isValidI18nCode(i18n)) {
			i18n = "zh-CN"; // 默认使用中文
		}

		RetKit ret = FontService.me.localeList(i18n);
		renderJson(ret);
	}

	/**
	 * 验证国际化代码是否有效
	 */
	private boolean isValidI18nCode(String i18n) {
		String[] validCodes = { "zh-CN", "en-US", "zh-HK", "ko-KR", "ja-JP",
				"fr-FR", "de-DE", "it-IT", "pt-PT", "es-ES",
				"vi-VN", "th-TH", "ru-RU", "zh-TW" };
		for (String code : validCodes) {
			if (code.equals(i18n)) {
				return true;
			}
		}
		return false;
	}
}
