package com.sandu.xinye.api.font;

import com.jfinal.kit.Kv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.sandu.xinye.common.model.Font;
import com.sandu.xinye.common.model.FontLocale;

import java.util.List;

public class FontService {
    public static final FontService me = new FontService();

    public RetKit list(int pageSize, int pageNumber, Kv kv) {
        // 生成缓存key
        String cacheKey = CacheConstant.FONT_LIST + "_" + pageSize + "_" + pageNumber + "_" + kv.toString();

        // 尝试从缓存获取
        Page<Font> cachedPage = CacheKit.get(CacheConstant.FONT, cacheKey);
        if (cachedPage != null) {
            return RetKit.ok("data", cachedPage);
        }

        if (kv.containsKey("isNew")) {
            // isNew和locale是互斥的
            kv.remove("locale");
        }

        SqlPara sqlPara = Db.getSqlPara("app.font.paginate", kv);
        Page<Font> page = Font.dao.paginate(pageNumber, pageSize, sqlPara);

        // 存入缓存
        CacheKit.put(CacheConstant.FONT, cacheKey, page);

        return RetKit.ok("data", page);
    }

    public RetKit localeList(String i18n) {
        // 生成缓存key
        String cacheKey = CacheConstant.FONT_LOCALE_LIST + "_" + i18n;

        // 尝试从缓存获取
        List<FontLocale> cachedList = CacheKit.get(CacheConstant.FONT, cacheKey);
        if (cachedList != null) {
            return RetKit.ok("data", cachedList);
        }

        List<FontLocale> list = FontLocale.dao.find("select locale, name from font_locale where i18n=?", i18n);

        // 存入缓存
        CacheKit.put(CacheConstant.FONT, cacheKey, list);

        return RetKit.ok("data", list);
    }

    /**
     * 清除字体相关缓存
     * 当字体数据发生变化时调用
     */
    public void clearCache() {
        CacheKit.removeAll(CacheConstant.FONT);
    }
}
