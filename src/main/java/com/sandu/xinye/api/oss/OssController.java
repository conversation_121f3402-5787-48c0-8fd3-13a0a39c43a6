package com.sandu.xinye.api.oss;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.interceptor.HybridRateLimitInterceptor;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.annotation.RateLimit;

import java.util.List;

public class OssController extends AppController {

    /**
     * 上传图片到OSS对象存储。
     * <p>
     * 前端通过表单上传图片文件，参数名为 "file"。
     * 上传成功后返回上传结果及图片的OSS地址。
     * </p>
     */
    @Before({AttackAntiInterceptor.class, HybridRateLimitInterceptor.class, PostOnlyInterceptor.class})
    @RateLimit(value = 30, ipLimit = 60) // 用户级限流：每分钟30次，IP级限流：每分钟60次
    public void uploadImg() {
        // 获取当前登录用户
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("未登录！"));
            return;
        }

        UploadFile uf = null;
        try {
            uf = getFile("file");
            if (uf == null) {
                renderJson(RetKit.fail("上传文件不能为空！"));
                return;
            }
        } catch (Exception e) {
            LogKit.error("获取上传文件失败：" + e.getMessage());
            renderJson(RetKit.fail("获取上传文件失败！"));
            return;
        }

        RetKit ret = OssService.me.uploadImg(uf, user.getUserId().toString());
        renderJson(ret);
    }

    /**
     * 批量上传图片到OSS对象存储。
     * <p>
     * 前端通过表单上传多个图片文件，参数名为 "file"。
     * 支持最多10张图片同时上传。
     * 上传成功后返回上传结果及图片的OSS地址列表。
     * </p>
     */
    @Before({AttackAntiInterceptor.class, HybridRateLimitInterceptor.class, PostOnlyInterceptor.class})
    @RateLimit(value = 6, ipLimit = 20) // 用户级限流：每分钟6次，IP级限流：每分钟20次（比单图上传更严格）
    public void uploadImgBatch() {
        // 获取当前登录用户
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("未登录！"));
            return;
        }

        List<UploadFile> ufs = null;
        try {
            ufs = getFiles("file");
            if (ufs == null || ufs.isEmpty()) {
                renderJson(RetKit.fail("上传文件不能为空！"));
                return;
            }
        } catch (Exception e) {
            LogKit.error("获取上传文件失败：" + e.getMessage());
            renderJson(RetKit.fail("获取上传文件失败！"));
            return;
        }

        RetKit ret = OssService.me.uploadImgBatch(ufs, user.getUserId().toString());
        renderJson(ret);
    }

    /**
     * 批量上传图片到OSS对象存储（严格模式）。
     * <p>
     * 如果文件数量超过10张，直接拒绝所有文件，不进行任何上传。
     * 适用于需要严格控制上传数量的场景。
     * </p>
     */
    @Before({AttackAntiInterceptor.class, HybridRateLimitInterceptor.class, PostOnlyInterceptor.class})
    @RateLimit(value = 3, ipLimit = 20) // 用户级限流：每分钟3次，IP级限流：每分钟20次
    public void uploadImgBatchStrict() {
        // 获取当前登录用户
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("未登录！"));
            return;
        }

        List<UploadFile> ufs = null;
        try {
            ufs = getFiles("file");
            if (ufs == null || ufs.isEmpty()) {
                renderJson(RetKit.fail("上传文件不能为空！"));
                return;
            }
        } catch (Exception e) {
            LogKit.error("获取上传文件失败：" + e.getMessage());
            renderJson(RetKit.fail("获取上传文件失败！"));
            return;
        }

        RetKit ret = OssService.me.uploadImgBatchStrict(ufs, user.getUserId().toString());
        renderJson(ret);
    }
}
