package com.sandu.xinye.api.oss;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.*;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.imageio.ImageIO;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class OssService {

	public static final OssService me = new OssService();

	private static final String OSS_OBJECT_PREFIX_IMG = "img/tattoogo";
	private static String stsTokenServer = "http://************:7080";
	Prop p = PropKit.use("common_config.txt");

	{
		String fileName = p.get("sqlConfig");
		p.append(fileName);
		stsTokenServer = p.get("stsTokenServer");
	}

	/**
	 * @return
	 * @Title: getAssumeRole
	 * @Description:
	 * <AUTHOR>
	 */
	public RetKit getAssumeRole() {
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpGet request = new HttpGet(stsTokenServer);

		try (CloseableHttpResponse response = httpClient.execute(request)) {
			int statusCode = response.getStatusLine().getStatusCode();

			if (statusCode == 200) {
				String result = EntityUtils.toString(response.getEntity(), "UTF-8");
				System.out.println("API返回结果：" + result);
				String assumeRoleStr = result.replaceAll("\n", "");
				System.out.println("API返回结果：" + assumeRoleStr);
				JSONObject assumeRoleObject = JSONUtil.parseObj(assumeRoleStr);
				if (assumeRoleObject.get("StatusCode").equals("200")) {
					return RetKit.ok().set("data", assumeRoleObject);
				} else {
					return RetKit.fail(assumeRoleObject.get("ErrorMessage"));
				}

			} else {
				System.out.println("API请求失败，错误码：" + statusCode);
			}
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		return RetKit.fail("请求STS临时凭证失败!");
	}

	/**
	 *
	 * 阿里云OSS上传图片
	 *
	 * @param uf     上传文件
	 * @param userId 用户ID
	 * @return
	 */
	public RetKit uploadImg(UploadFile uf, String userId) {
		LogKit.info("uploadImg=== 上传图片");
		if (uf == null) {
			LogKit.error("=== 上传图片不能为空");
			return RetKit.fail("上传图片不能为空！");
		}
		if (userId == null || userId.trim().isEmpty()) {
			LogKit.error("=== 用户ID不能为空");
			return RetKit.fail("用户ID不能为空！");
		}

		try {
			// 1. 文件类型校验
			if (!ImageKit.isImageExtName(uf.getFileName())) {
				LogKit.error("uploadImg=== 文件类型不正确");
				return RetKit.fail("msg", "文件类型不正确，只支持jpg、jpeg、png、bmp");
			}
			// 2. 文件大小限制（10MB）
			if (uf.getFile().length() > 10 * 1024 * 1024) {
				LogKit.error("uploadImg=== 文件过大");
				return RetKit.fail("msg", "文件过大，最大支持10MB");
			}
			// 3. 文件内容校验（防伪造图片）
			try {
				if (ImageIO.read(uf.getFile()) == null) {
					LogKit.error("uploadImg=== 文件内容不是有效图片");
					return RetKit.fail("msg", "文件内容不是有效图片");
				}
			} catch (Exception e) {
				LogKit.error("uploadImg=== 图片内容校验异常");
				return RetKit.fail("msg", "文件内容不是有效图片");
			}
			// 4. 文件名安全处理（用UUID重命名）
			String ext = MyFileKit.getExtName(uf.getFileName());
			String safeFileName = UUID.randomUUID().toString().replace("-", "") + "." + ext;
			// 5. 上传到OSS，传递安全文件名，加入用户ID到路径中
			String userSpecificPath = OSS_OBJECT_PREFIX_IMG + "/" + userId;
			String ossFileUrl = AliOssKit.upload(userSpecificPath, uf, safeFileName);
			return RetKit.ok().set("data", ossFileUrl);
		} catch (Exception e) {
			LogKit.error("Upload file failed", e);
			return RetKit.fail("msg", "上传失败，请稍后重试");
		} finally {
			if (uf != null && uf.getFile() != null) {
				LogKit.info("清除临时文件");
				uf.getFile().delete();
			}
		}
	}

	/**
	 * 批量上传图片到OSS对象存储
	 *
	 * @param ufs    上传文件列表
	 * @param userId 用户ID
	 * @return
	 */
	public RetKit uploadImgBatch(List<UploadFile> ufs, String userId) {
		LogKit.info("uploadImgBatch=== 批量上传图片");
		if (ufs == null || ufs.isEmpty()) {
			LogKit.error("=== 批量上传图片不能为空");
			return RetKit.fail("上传图片不能为空！");
		}
		if (userId == null || userId.trim().isEmpty()) {
			LogKit.error("=== 用户ID不能为空");
			return RetKit.fail("用户ID不能为空！");
		}

		// 限制批量上传数量（最多10张）
		if (ufs.size() > 10) {
			LogKit.error("uploadImgBatch=== 批量上传数量超限");
			return RetKit.fail("msg", "批量上传最多支持10张图片");
		}

		List<String> successUrls = new ArrayList<>();
		List<String> failedFiles = new ArrayList<>();

		try {
			for (int i = 0; i < ufs.size(); i++) {
				UploadFile uf = ufs.get(i);
				if (uf == null) {
					failedFiles.add("文件" + (i + 1) + ": 文件为空");
					continue;
				}

				try {
					// 1. 文件类型校验
					if (!ImageKit.isImageExtName(uf.getFileName())) {
						LogKit.error("uploadImgBatch=== 文件类型不正确: " + uf.getFileName());
						failedFiles.add("文件" + (i + 1) + ": 文件类型不正确，只支持jpg、jpeg、png、bmp");
						continue;
					}
					// 2. 文件大小限制（10MB）
					if (uf.getFile().length() > 10 * 1024 * 1024) {
						LogKit.error("uploadImgBatch=== 文件过大: " + uf.getFileName());
						failedFiles.add("文件" + (i + 1) + ": 文件过大，最大支持10MB");
						continue;
					}
					// 3. 文件内容校验（防伪造图片）
					try {
						if (ImageIO.read(uf.getFile()) == null) {
							LogKit.error("uploadImgBatch=== 文件内容不是有效图片: " + uf.getFileName());
							failedFiles.add("文件" + (i + 1) + ": 文件内容不是有效图片");
							continue;
						}
					} catch (Exception e) {
						LogKit.error("uploadImgBatch=== 图片内容校验异常: " + uf.getFileName());
						failedFiles.add("文件" + (i + 1) + ": 文件内容不是有效图片");
						continue;
					}
					// 4. 文件名安全处理（用UUID重命名）
					String ext = MyFileKit.getExtName(uf.getFileName());
					String safeFileName = UUID.randomUUID().toString().replace("-", "") + "." + ext;
					// 5. 上传到OSS，传递安全文件名，加入用户ID到路径中
					String userSpecificPath = OSS_OBJECT_PREFIX_IMG + "/" + userId;
					String ossFileUrl = AliOssKit.upload(userSpecificPath, uf, safeFileName);

					if (ossFileUrl != null && !ossFileUrl.trim().isEmpty()) {
						successUrls.add(ossFileUrl);
						LogKit.info("uploadImgBatch=== 文件上传成功: " + uf.getFileName() + " -> " + ossFileUrl);
					} else {
						failedFiles.add("文件" + (i + 1) + ": 上传到OSS失败");
					}
				} catch (Exception e) {
					LogKit.error("uploadImgBatch=== 单个文件上传失败: " + uf.getFileName(), e);
					failedFiles.add("文件" + (i + 1) + ": 上传失败 - " + e.getMessage());
				}
			}

			// 构建返回结果
			if (successUrls.isEmpty()) {
				return RetKit.fail("msg", "所有文件上传失败").set("failedFiles", failedFiles);
			} else if (failedFiles.isEmpty()) {
				return RetKit.ok().set("data", successUrls).set("message", "所有文件上传成功");
			} else {
				return RetKit.ok()
					.set("data", successUrls)
					.set("failedFiles", failedFiles)
					.set("message", "部分文件上传成功，成功: " + successUrls.size() + "个，失败: " + failedFiles.size() + "个");
			}
		} catch (Exception e) {
			LogKit.error("uploadImgBatch=== 批量上传异常", e);
			return RetKit.fail("msg", "批量上传失败，请稍后重试");
		} finally {
			// 清理临时文件
			for (UploadFile uf : ufs) {
				if (uf != null && uf.getFile() != null) {
					try {
						uf.getFile().delete();
						LogKit.info("清除临时文件: " + uf.getFile().getName());
					} catch (Exception e) {
						LogKit.error("清除临时文件失败: " + uf.getFile().getName(), e);
					}
				}
			}
		}
	}

}