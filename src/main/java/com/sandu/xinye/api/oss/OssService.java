package com.sandu.xinye.api.oss;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.*;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.imageio.ImageIO;
import java.io.IOException;
import java.util.UUID;

public class OssService {

	public static final OssService me = new OssService();

	private static final String OSS_OBJECT_PREFIX_IMG = "img/tattoogo";
	private static String stsTokenServer = "http://************:7080";
	Prop p = PropKit.use("common_config.txt");

	{
		String fileName = p.get("sqlConfig");
		p.append(fileName);
		stsTokenServer = p.get("stsTokenServer");
	}

	/**
	 * @return
	 * @Title: getAssumeRole
	 * @Description:
	 * <AUTHOR>
	 */
	public RetKit getAssumeRole() {
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpGet request = new HttpGet(stsTokenServer);

		try (CloseableHttpResponse response = httpClient.execute(request)) {
			int statusCode = response.getStatusLine().getStatusCode();

			if (statusCode == 200) {
				String result = EntityUtils.toString(response.getEntity(), "UTF-8");
				System.out.println("API返回结果：" + result);
				String assumeRoleStr = result.replaceAll("\n", "");
				System.out.println("API返回结果：" + assumeRoleStr);
				JSONObject assumeRoleObject = JSONUtil.parseObj(assumeRoleStr);
				if (assumeRoleObject.get("StatusCode").equals("200")) {
					return RetKit.ok().set("data", assumeRoleObject);
				} else {
					return RetKit.fail(assumeRoleObject.get("ErrorMessage"));
				}

			} else {
				System.out.println("API请求失败，错误码：" + statusCode);
			}
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		return RetKit.fail("请求STS临时凭证失败!");
	}

	/**
	 *
	 * 阿里云OSS上传图片
	 *
	 * @param uf     上传文件
	 * @param userId 用户ID
	 * @return
	 */
	public RetKit uploadImg(UploadFile uf, String userId) {
		LogKit.info("uploadImg=== 上传图片");
		if (uf == null) {
			LogKit.error("=== 上传图片不能为空");
			return RetKit.fail("上传图片不能为空！");
		}
		if (userId == null || userId.trim().isEmpty()) {
			LogKit.error("=== 用户ID不能为空");
			return RetKit.fail("用户ID不能为空！");
		}

		try {
			// 1. 文件类型校验
			if (!ImageKit.isImageExtName(uf.getFileName())) {
				LogKit.error("uploadImg=== 文件类型不正确");
				return RetKit.fail("msg", "文件类型不正确，只支持jpg、jpeg、png、bmp");
			}
			// 2. 文件大小限制（10MB）
			if (uf.getFile().length() > 10 * 1024 * 1024) {
				LogKit.error("uploadImg=== 文件过大");
				return RetKit.fail("msg", "文件过大，最大支持10MB");
			}
			// 3. 文件内容校验（防伪造图片）
			try {
				if (ImageIO.read(uf.getFile()) == null) {
					LogKit.error("uploadImg=== 文件内容不是有效图片");
					return RetKit.fail("msg", "文件内容不是有效图片");
				}
			} catch (Exception e) {
				LogKit.error("uploadImg=== 图片内容校验异常");
				return RetKit.fail("msg", "文件内容不是有效图片");
			}
			// 4. 文件名安全处理（用UUID重命名）
			String ext = MyFileKit.getExtName(uf.getFileName());
			String safeFileName = UUID.randomUUID().toString().replace("-", "") + "." + ext;
			// 5. 上传到OSS，传递安全文件名，加入用户ID到路径中
			String userSpecificPath = OSS_OBJECT_PREFIX_IMG + "/" + userId;
			String ossFileUrl = AliOssKit.upload(userSpecificPath, uf, safeFileName);
			return RetKit.ok().set("data", ossFileUrl);
		} catch (Exception e) {
			LogKit.error("Upload file failed", e);
			return RetKit.fail("msg", "上传失败，请稍后重试");
		} finally {
			if (uf != null && uf.getFile() != null) {
				LogKit.info("清除临时文件");
				uf.getFile().delete();
			}
		}
	}

}