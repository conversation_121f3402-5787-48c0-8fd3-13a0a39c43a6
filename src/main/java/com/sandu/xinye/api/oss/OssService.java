package com.sandu.xinye.api.oss;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.config.OssConfig;
import com.sandu.xinye.common.kit.*;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.imageio.ImageIO;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class OssService {

	public static final OssService me = new OssService();

	private static final String OSS_OBJECT_PREFIX_IMG = "img/tattoogo";
	private static String stsTokenServer = "http://************:7080";
	Prop p = PropKit.use("common_config.txt");

	{
		String fileName = p.get("sqlConfig");
		p.append(fileName);
		stsTokenServer = p.get("stsTokenServer");
	}

	/**
	 * @return
	 * @Title: getAssumeRole
	 * @Description:
	 * <AUTHOR>
	 */
	public RetKit getAssumeRole() {
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpGet request = new HttpGet(stsTokenServer);

		try (CloseableHttpResponse response = httpClient.execute(request)) {
			int statusCode = response.getStatusLine().getStatusCode();

			if (statusCode == 200) {
				String result = EntityUtils.toString(response.getEntity(), "UTF-8");
				System.out.println("API返回结果：" + result);
				String assumeRoleStr = result.replaceAll("\n", "");
				System.out.println("API返回结果：" + assumeRoleStr);
				JSONObject assumeRoleObject = JSONUtil.parseObj(assumeRoleStr);
				if (assumeRoleObject.get("StatusCode").equals("200")) {
					return RetKit.ok().set("data", assumeRoleObject);
				} else {
					return RetKit.fail(assumeRoleObject.get("ErrorMessage"));
				}

			} else {
				System.out.println("API请求失败，错误码：" + statusCode);
			}
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		return RetKit.fail("请求STS临时凭证失败!");
	}

	/**
	 *
	 * 阿里云OSS上传图片
	 *
	 * @param uf     上传文件
	 * @param userId 用户ID
	 * @return
	 */
	public RetKit uploadImg(UploadFile uf, String userId) {
		LogKit.info("uploadImg=== 上传图片");
		if (uf == null) {
			LogKit.error("=== 上传图片不能为空");
			return RetKit.fail("msg", OssConfig.MessageKeys.UPLOAD_FILE_EMPTY);
		}
		if (userId == null || userId.trim().isEmpty()) {
			LogKit.error("=== 用户ID不能为空");
			return RetKit.fail("msg", OssConfig.MessageKeys.USER_ID_EMPTY);
		}

		try {
			// 1. 文件类型校验
			if (!ImageKit.isImageExtName(uf.getFileName())) {
				LogKit.error("uploadImg=== 文件类型不正确");
				return RetKit.fail("msg", OssConfig.MessageKeys.FILE_TYPE_INVALID);
			}
			// 2. 文件大小限制
			if (OssConfig.isSingleFileSizeExceeded(uf.getFile().length())) {
				LogKit.error("uploadImg=== 文件过大");
				return RetKit.fail("msg", OssConfig.MessageKeys.FILE_SIZE_EXCEEDED);
			}
			// 3. 文件内容校验（防伪造图片）
			try {
				if (ImageIO.read(uf.getFile()) == null) {
					LogKit.error("uploadImg=== 文件内容不是有效图片");
					return RetKit.fail("msg", OssConfig.MessageKeys.FILE_CONTENT_INVALID);
				}
			} catch (Exception e) {
				LogKit.error("uploadImg=== 图片内容校验异常");
				return RetKit.fail("msg", OssConfig.MessageKeys.FILE_CONTENT_INVALID);
			}
			// 4. 文件名安全处理（用UUID重命名）
			String ext = MyFileKit.getExtName(uf.getFileName());
			String safeFileName = UUID.randomUUID().toString().replace("-", "") + "." + ext;
			// 5. 上传到OSS，传递安全文件名，加入用户ID到路径中
			String userSpecificPath = OSS_OBJECT_PREFIX_IMG + "/" + userId;
			String ossFileUrl = AliOssKit.upload(userSpecificPath, uf, safeFileName);
			return RetKit.ok().set("data", ossFileUrl);
		} catch (Exception e) {
			LogKit.error("Upload file failed", e);
			return RetKit.fail("msg", OssConfig.MessageKeys.UPLOAD_FAILED);
		} finally {
			if (uf != null && uf.getFile() != null) {
				LogKit.info("清除临时文件");
				uf.getFile().delete();
			}
		}
	}

	/**
	 * 批量上传图片到OSS对象存储
	 *
	 * @param ufs    上传文件列表
	 * @param userId 用户ID
	 * @return
	 */
	public RetKit uploadImgBatch(List<UploadFile> ufs, String userId) {
		LogKit.info("uploadImgBatch=== 批量上传图片");
		if (ufs == null || ufs.isEmpty()) {
			LogKit.error("=== 批量上传图片不能为空");
			return RetKit.fail("msg", OssConfig.MessageKeys.UPLOAD_FILE_EMPTY);
		}
		if (userId == null || userId.trim().isEmpty()) {
			LogKit.error("=== 用户ID不能为空");
			return RetKit.fail("msg", OssConfig.MessageKeys.USER_ID_EMPTY);
		}

		// 限制批量上传数量
		boolean hasExceededLimit = false;
		int maxCount = OssConfig.getBatchUploadMaxCount();
		if (OssConfig.isBatchCountExceeded(ufs.size())) {
			LogKit.warn("uploadImgBatch=== 批量上传数量超限，只处理前" + maxCount + "张图片。总数: " + ufs.size());
			hasExceededLimit = true;
			// 只处理前N张图片
			ufs = ufs.subList(0, maxCount);
		}

		List<String> successUrls = new ArrayList<>();
		List<String> failedFiles = new ArrayList<>();

		try {
			for (int i = 0; i < ufs.size(); i++) {
				UploadFile uf = ufs.get(i);
				if (uf == null) {
					failedFiles.add("文件" + (i + 1) + ": 文件为空");
					continue;
				}

				try {
					// 1. 文件类型校验
					if (!ImageKit.isImageExtName(uf.getFileName())) {
						LogKit.error("uploadImgBatch=== 文件类型不正确: " + uf.getFileName());
						failedFiles.add("文件" + (i + 1) + ": " + OssConfig.MessageKeys.FILE_TYPE_INVALID);
						continue;
					}
					// 2. 文件大小限制
					if (OssConfig.isSingleFileSizeExceeded(uf.getFile().length())) {
						LogKit.error("uploadImgBatch=== 文件过大: " + uf.getFileName());
						failedFiles.add("文件" + (i + 1) + ": " + OssConfig.MessageKeys.FILE_SIZE_EXCEEDED);
						continue;
					}
					// 3. 文件内容校验（防伪造图片）
					try {
						if (ImageIO.read(uf.getFile()) == null) {
							LogKit.error("uploadImgBatch=== 文件内容不是有效图片: " + uf.getFileName());
							failedFiles.add("文件" + (i + 1) + ": " + OssConfig.MessageKeys.FILE_CONTENT_INVALID);
							continue;
						}
					} catch (Exception e) {
						LogKit.error("uploadImgBatch=== 图片内容校验异常: " + uf.getFileName());
						failedFiles.add("文件" + (i + 1) + ": " + OssConfig.MessageKeys.FILE_CONTENT_INVALID);
						continue;
					}
					// 4. 文件名安全处理（用UUID重命名）
					String ext = MyFileKit.getExtName(uf.getFileName());
					String safeFileName = UUID.randomUUID().toString().replace("-", "") + "." + ext;
					// 5. 上传到OSS，传递安全文件名，加入用户ID到路径中
					String userSpecificPath = OSS_OBJECT_PREFIX_IMG + "/" + userId;
					String ossFileUrl = AliOssKit.upload(userSpecificPath, uf, safeFileName);

					if (ossFileUrl != null && !ossFileUrl.trim().isEmpty()) {
						successUrls.add(ossFileUrl);
						LogKit.info("uploadImgBatch=== 文件上传成功: " + uf.getFileName() + " -> " + ossFileUrl);
					} else {
						failedFiles.add("文件" + (i + 1) + ": 上传到OSS失败");
					}
				} catch (Exception e) {
					LogKit.error("uploadImgBatch=== 单个文件上传失败: " + uf.getFileName(), e);
					failedFiles.add("文件" + (i + 1) + ": 上传失败 - " + e.getMessage());
				}
			}

			// 构建返回结果
			RetKit result;
			if (successUrls.isEmpty()) {
				result = RetKit.fail("msg", OssConfig.MessageKeys.ALL_FILES_FAILED).set("failedFiles", failedFiles);
			} else if (failedFiles.isEmpty() && !hasExceededLimit) {
				result = RetKit.ok().set("data", successUrls).set("message", OssConfig.MessageKeys.ALL_FILES_SUCCESS);
			} else {
				String messageKey;
				if (hasExceededLimit && failedFiles.isEmpty()) {
					messageKey = OssConfig.MessageKeys.SUCCESS_WITH_LIMIT;
				} else if (hasExceededLimit && !failedFiles.isEmpty()) {
					messageKey = OssConfig.MessageKeys.PARTIAL_FILES_SUCCESS_WITH_LIMIT;
				} else {
					messageKey = OssConfig.MessageKeys.PARTIAL_FILES_SUCCESS;
				}
				result = RetKit.ok()
					.set("data", successUrls)
					.set("failedFiles", failedFiles)
					.set("message", messageKey)
					.set("successCount", successUrls.size())
					.set("failedCount", failedFiles.size())
					.set("maxCount", maxCount);
			}

			// 如果超出数量限制，添加警告信息
			if (hasExceededLimit) {
				result.set("warning", OssConfig.MessageKeys.BATCH_COUNT_LIMIT_WARNING)
					  .set("maxCount", maxCount);
			}

			return result;
		} catch (Exception e) {
			LogKit.error("uploadImgBatch=== 批量上传异常", e);
			return RetKit.fail("msg", OssConfig.MessageKeys.UPLOAD_FAILED);
		} finally {
			// 清理临时文件
			for (UploadFile uf : ufs) {
				if (uf != null && uf.getFile() != null) {
					try {
						uf.getFile().delete();
						LogKit.info("清除临时文件: " + uf.getFile().getName());
					} catch (Exception e) {
						LogKit.error("清除临时文件失败: " + uf.getFile().getName(), e);
					}
				}
			}
		}
	}

	/**
	 * 批量上传图片到OSS对象存储（严格模式）
	 * 如果文件数量超出限制，直接拒绝所有文件
	 *
	 * @param ufs    上传文件列表
	 * @param userId 用户ID
	 * @return
	 */
	public RetKit uploadImgBatchStrict(List<UploadFile> ufs, String userId) {
		LogKit.info("uploadImgBatchStrict=== 批量上传图片（严格模式）");
		if (ufs == null || ufs.isEmpty()) {
			LogKit.error("=== 批量上传图片不能为空");
			return RetKit.fail("msg", OssConfig.MessageKeys.UPLOAD_FILE_EMPTY);
		}
		if (userId == null || userId.trim().isEmpty()) {
			LogKit.error("=== 用户ID不能为空");
			return RetKit.fail("msg", OssConfig.MessageKeys.USER_ID_EMPTY);
		}

		// 严格模式：超出数量限制直接拒绝
		if (OssConfig.isBatchCountExceeded(ufs.size())) {
			LogKit.error("uploadImgBatchStrict=== 批量上传数量超限，严格模式拒绝处理");
			return RetKit.fail("msg", OssConfig.MessageKeys.BATCH_COUNT_EXCEEDED_STRICT)
				.set("maxCount", OssConfig.getBatchUploadMaxCount())
				.set("actualCount", ufs.size());
		}

		// 调用普通批量上传方法
		return uploadImgBatch(ufs, userId);
	}

}