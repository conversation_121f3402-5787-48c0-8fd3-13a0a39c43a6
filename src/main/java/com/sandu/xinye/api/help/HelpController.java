package com.sandu.xinye.api.help;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.admin.help.HelpService;
import com.sandu.xinye.api.material.MaterialService;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.kit.RetKit;

@Clear
@Before({I18nInterceptor.class})
public class HelpController extends AdminController {
	
	public void index(){
		render("/admin_index.html");
	}

	public void list(){
		int pageNumber = getParaToInt("pageNumber",1);
		int pageSize = getParaToInt("pageSize",10);
		RetKit ret = HelpService.me.list(pageSize, pageNumber,getParaToMap());
		renderJson(ret);
	}

}
