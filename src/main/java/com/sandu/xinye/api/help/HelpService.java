package com.sandu.xinye.api.help;

import com.jfinal.kit.Kv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Density;
import com.sandu.xinye.common.model.HelpCenter;
import com.sandu.xinye.common.model.Material;

import java.util.List;

public class HelpService {
    public static final HelpService me = new HelpService();

    public RetKit list(int pageSize, int pageNumber, Kv kv) {
        SqlPara sqlPara = Db.getSqlPara("admin.helpcenter.paginate", kv);
        Page<HelpCenter> page = HelpCenter.dao.paginate(pageNumber, pageSize, sqlPara);
        return RetKit.ok("data", page);
    }

}
