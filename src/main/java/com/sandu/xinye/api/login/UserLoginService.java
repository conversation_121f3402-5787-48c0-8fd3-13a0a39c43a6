package com.sandu.xinye.api.login;

import com.jfinal.kit.HashKit;
import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.kit.RandomKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.AliOpenapiKit;
import com.sandu.xinye.common.kit.EmailKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.UserSession;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class UserLoginService {

    public static final UserLoginService me = new UserLoginService();

    private static boolean containsEmoji(String source) {
        int len = source.length();
        boolean isEmoji = false;
        for (int i = 0; i < len; i++) {
            char hs = source.charAt(i);
            if (0xd800 <= hs && hs <= 0xdbff) {
                if (source.length() > 1) {
                    char ls = source.charAt(i + 1);
                    int uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
                    if (0x1d000 <= uc && uc <= 0x1f77f) {
                        return true;
                    }
                }
            } else {
                // non surrogate
                if (0x2100 <= hs && hs <= 0x27ff && hs != 0x263b) {
                    return true;
                } else if (0x2B05 <= hs && hs <= 0x2b07) {
                    return true;
                } else if (0x2934 <= hs && hs <= 0x2935) {
                    return true;
                } else if (0x3297 <= hs && hs <= 0x3299) {
                    return true;
                } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030 || hs == 0x2b55 || hs == 0x2b1c
                        || hs == 0x2b1b || hs == 0x2b50 || hs == 0x231a) {
                    return true;
                }
                if (!isEmoji && source.length() > 1 && i < source.length() - 1) {
                    char ls = source.charAt(i + 1);
                    if (ls == 0x20e3) {
                        return true;
                    }
                }
            }
        }

        // 双重过滤
        Pattern emoji = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]",
                Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
        Matcher emojiMatcher = emoji.matcher(source);
        if (emojiMatcher.find()) {
            isEmoji = true;
        }

        return isEmoji;
    }

    public RetKit register(String phone, String password, String captcha, String ipAddress) {
        if (StrKit.isBlank(phone) || StrKit.isBlank(password) || StrKit.isBlank(captcha)) {
            return RetKit.fail("手机号、密码、验证码不能为空！");
        }
        phone = phone.trim();
        User user = User.dao.findFirst("select * from user where userPhone=?", phone);
        if (user != null) {
            return RetKit.fail("该手机号码已被注册！");
        }
        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
        System.out.println(realCaptcha + "验证码");

        if (realCaptcha == null) {
            return RetKit.fail("验证码已过期，请重新发送验证码！");
        }
        if (!captcha.equals(realCaptcha)) {
            return RetKit.fail("验证码错误！");
        }
        String userNickName = "User" + RandomKit.generateSixDigitCode();
        String salt = HashKit.generateSaltForSha256();
        String hashPwd = HashKit.sha256(salt + password);
        User newUser = new User();
        boolean succ = newUser.setUserNickName(userNickName).setUserPhone(phone)
                .setUserPass(hashPwd).setRegisterType(User.REGISTER_TYPE_PHONE).setSalt(salt)
                .setStatus(User.STATUS_IS_REGISTER).setCreateTime(new Date()).save();
        if (succ) {
            CacheKit.remove(CacheConstant.CAPTCHA, phone);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit unregister(Integer userId, String accessToken) {
        if (StrKit.isBlank(accessToken)) {
            return RetKit.fail("未登录！");
        }

        UserSession session = UserSession.dao.findById(accessToken);
        if (session == null) {
            return RetKit.fail("未登录或会话已过期！");
        }

        if (!session.getUserId().equals(userId)) {
            return RetKit.fail("错误的用户ID！");
        }

        Db.tx(() -> {
            // Remove everything related to specific userId in the database
            Db.delete("delete from user_login_log where userId = ?", userId);
            Db.delete("delete from templet where userId = ?", userId);
            Db.delete("delete from templet_group where userId = ?", userId);
            Db.delete("delete from feedback where userId = ?", userId);
            User.dao.deleteById(userId);

            // TODO: You must delete the userImg either if they are uploaded as
            // a file.
            return true;
        });

        // Logout after unregisterred specific user.
        logout(accessToken);

        return RetKit.ok();
    }

    public RetKit doLogin(String phone, String password, String platform, String ipAddress) {
        if (StrKit.isBlank(phone) || StrKit.isBlank(password)) {
            return RetKit.fail("手机号和密码不能为空！");
        }
        phone = phone.trim();

        // 检查手机号是否在黑名单中
        if (isAccountInBlacklist(phone)) {
            return RetKit.fail("该账号已被注销，7天内不能重新注册登录！");
        }

        User user = User.dao.findFirst("select * from user where userPhone=? ", phone);
        if (user == null) {
            return RetKit.fail("该手机号码未注册！");
        }

        String salt = user.getSalt();
        String hashPwd = HashKit.sha256(salt + password);
        if (hashPwd.equals(user.getUserPass())) {
            // 登录有效时间 7天(秒)
            long liveSeconds = 30 * 24 * 60 * 60;
            // 过期的时间戳
            long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
            // 生成accessToken
            String accessToken = HashKit.sha1(StrKit.getRandomUUID());
            // 创建session
            UserSession session = new UserSession();
            session.setUserId(user.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp)
                    .setCreateTime(new Date()).save();
            // 记录登录时间
            user.setLastLoginTime(new Date()).update();
            // 清楚账号的盐和密码，不暴露出来
            user.removeSensitiveInfo();
            CacheKit.put(CacheConstant.APP_USER, accessToken, user);
            // 创建登录日志
            createLoginLog(user.getUserId(), ipAddress, Integer.valueOf(platform));
            Kv kv = new Kv();
            kv = Kv.by("userId", user.getUserId()).set("userNickName", user.getUserNickName()).set("userPhone", phone)
                    .set("userEmail", user.getUserEmail() == null ? "" : user.getUserEmail())
                    .set("userImg", user.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken);

            return RetKit.ok("data", kv).setMsg("登录成功");
        } else {
            return RetKit.fail("账号或密码错误");
        }
    }

    /**
     * 验证码登录
     *
     * @param phone
     * @param captcha
     * @param platform
     * @param ipAddress
     * @return
     */
    public RetKit captchaLogin(String phone, String captcha, String platform, String ipAddress) {
        if (StrKit.isBlank(phone) || StrKit.isBlank(captcha)) {
            return RetKit.fail("手机号和验证码不能为空！");
        }
        phone = phone.trim();

        // 检查手机号是否在黑名单中
        if (isAccountInBlacklist(phone)) {
            return RetKit.fail("该账号已被注销，7天内不能重新注册登录！");
        }

        // 1. 检查尝试次数和锁定状态（合并逻辑）
        String attemptKey = CacheConstant.CAPTCHA_ATTEMPT + phone;
        Object attemptValue = CacheKit.get(CacheConstant.CAPTCHA, attemptKey);

        // 2. 检查是否被锁定
        if (CacheConstant.CAPTCHA_LOCKED_VALUE.equals(attemptValue)) {
            return RetKit.fail("尝试次数过多，请稍后再试！");
        }

        // 3. 获取当前尝试次数
        int attemptCount = 0;
        if (attemptValue instanceof Integer) {
            attemptCount = (Integer) attemptValue;
        }

        // 4. 验证码校验
        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
        if (realCaptcha == null) {
            return RetKit.fail("验证码已过期，请重新发送验证码！");
        }
        if (!captcha.equals(realCaptcha)) {
            // 增加尝试次数
            int maxAttempts = 3; // 最大尝试次数
            attemptCount++;
            if (attemptCount >= maxAttempts) {
                // 达到最大尝试次数，锁定
                CacheKit.put(CacheConstant.CAPTCHA, attemptKey, CacheConstant.CAPTCHA_LOCKED_VALUE);
                return RetKit.fail("尝试次数过多，请稍后再试！");
            } else {
                // 记录尝试次数
                CacheKit.put(CacheConstant.CAPTCHA, attemptKey, attemptCount);
                return RetKit.fail("验证码错误！");
            }
        }

        // 5. 验证码正确，清除尝试次数
        CacheKit.remove(CacheConstant.CAPTCHA, attemptKey);

        User checkUser = User.dao.findFirst("select * from user where userPhone=? ", phone);
        if (checkUser != null) {
            // 检查用户是否在黑名单中
            if (isAccountInBlacklist(phone)) {
                return RetKit.fail("该账号已被注销，7天内不能重新注册登录！");
            }
        } else {
            // 创建用户账号
            String userNickName = "User" + RandomKit.generateSixDigitCode();
            User newUser = new User();
            try {
                boolean succ = newUser.setUserNickName(userNickName).setUserPhone(phone)
                        .setRegisterType(User.REGISTER_TYPE_PHONE)
                        .setStatus(User.STATUS_IS_REGISTER).setCreateTime(new Date()).save();
                if (succ) {
                    CacheKit.remove(CacheConstant.CAPTCHA, phone);
                } else {
                    return RetKit.fail("登录失败！");
                }
            } catch (Exception e) {
                LogKit.info("验证码登录失败，手机号：" + phone);
                LogKit.error("验证码登录失败:" + e.getMessage());
            }
            checkUser = newUser;
        }

        // 登录有效时间 7天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());
        UserSession session = new UserSession();
        session.setUserId(checkUser.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp)
                .setCreateTime(new Date()).save();
        checkUser.setLastLoginTime(new Date()).update();
        checkUser.removeSensitiveInfo();
        CacheKit.put(CacheConstant.APP_USER, accessToken, checkUser);
        createLoginLog(checkUser.getUserId(), ipAddress, Integer.valueOf(platform));
        Kv kv = new Kv();
        kv = Kv.by("userId", checkUser.getUserId()).set("userNickName", checkUser.getUserNickName())
                .set("userPhone", phone)
                .set("userEmail", checkUser.getUserEmail() == null ? "" : checkUser.getUserEmail())
                .set("userImg", checkUser.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken);

        return RetKit.ok("data", kv).setMsg("登录成功");
    }

    /**
     * @param openId
     * @param loginType
     * @param platform
     * @param avatar
     * @param nickName
     * @return
     * @Title: fasterLogin
     * @Description:
     * @date 2019年3月15日 下午2:33:16
     * <AUTHOR>
     */
    public RetKit fasterLogin(String openId, Integer loginType, String platform, String avatar, String nickName,
            String ip) {

        if (StrKit.isBlank(openId) || StrKit.isBlank(platform) || loginType == null) {
            return RetKit.fail("参数不能为空！");
        }
        if ((loginType != User.REGISTER_TYPE_QQ) && (loginType != User.REGISTER_TYPE_WX)) {
            return RetKit.fail("传参错误！");
        }
        StringBuffer sql = new StringBuffer();
        sql.append("select * from user where ");
        if (loginType == User.REGISTER_TYPE_QQ) {
            sql.append(" qqId=? ");
        } else {
            sql.append(" wxId=? ");
        }
        sql.append(" limit 1");

        User checkUser = User.dao.findFirst(sql.toString(), openId);

        if (checkUser == null) {

            String newNickName = nickName;

            if (containsEmoji(newNickName)) {
                LogKit.info("第三方登录用户昵称还有emoji：" + newNickName);
                if (loginType == User.REGISTER_TYPE_QQ) {
                    newNickName = "QQ用户_" + UUID.randomUUID().toString().replace("-", "").substring(0, 7);
                } else {
                    newNickName = "微信用户_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                }
            }

            User user = new User();
            user.setUserNickName(newNickName);
            user.setUserImg(avatar);
            if (loginType == User.REGISTER_TYPE_QQ) {
                user.setQqId(openId);
            } else {
                user.setWxId(openId);
            }
            user.setRegisterType(loginType);
            user.setStatus(User.STATUS_IS_REGISTER);
            user.setCreateTime(new Date());
            try {
                boolean succ = user.save();
                if (!succ) {
                    return RetKit.fail("登录失败！");
                }
            } catch (Exception e) {
                LogKit.info("第三方登录失败，用户昵称" + newNickName);
                LogKit.error("第三方登录失败：" + e.getMessage());
            }

            checkUser = user;
        }
        // 登录有效时间 7天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
        // 过期的时间戳
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        // 生成accessToken
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());
        // 创建session
        UserSession session = new UserSession();
        session.setUserId(checkUser.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp)
                .setCreateTime(new Date()).save();
        // 记录登录时间
        checkUser.setLastLoginTime(new Date()).update();
        // 清楚账号的盐和密码，不暴露出来
        checkUser.removeSensitiveInfo();
        // 放进缓存
        CacheKit.put(CacheConstant.APP_USER, accessToken, checkUser);
        // 创建登录日志
        createLoginLog(checkUser.getUserId(), ip, Integer.valueOf(platform));
        Kv kv = new Kv();
        kv = Kv.by("userId", checkUser.getUserId()).set("userNickName", checkUser.getUserNickName())
                .set("userPhone", checkUser.getUserPhone() == null ? "" : checkUser.getUserPhone())
                .set("userEmail", checkUser.getUserEmail() == null ? "" : checkUser.getUserEmail())
                .set("userImg", checkUser.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken);
        return RetKit.ok("data", kv).setMsg("登录成功");
    }

    public RetKit appleLogin(String appleUserId, int loginType, String platform, String ip) {
        if (StrKit.isBlank(appleUserId)) {
            return RetKit.fail("参数不能为空！");
        }
        User exist = User.dao.findFirst("select * from user where appleLoginUserId=?", appleUserId.trim());
        if (exist == null) {
            exist = new User();
            String nickName = "苹果用户_" + UUID.randomUUID().toString().replace("-", "").substring(0, 7);
            exist.setUserNickName(nickName).setStatus(User.STATUS_IS_LOGINED)
                    .setCreateTime(new Date()).setAppleLoginUserId(appleUserId);
            exist.save();
        }
        // 登录有效时间 7天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
        // 过期的时间戳
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        // 生成accessToken
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());
        // 创建session
        UserSession session = new UserSession();
        session.setUserId(exist.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp).setCreateTime(new Date())
                .save();
        // 记录登录时间
        exist.setLastLoginTime(new Date()).update();
        // 清除账号的密码和盐
        exist.removeSensitiveInfo();
        // 放进缓存
        CacheKit.put(CacheConstant.APP_USER, accessToken, exist);
        // 创建登录日志
        createLoginLog(exist.getUserId(), ip, Integer.valueOf(platform));
        Kv kv = new Kv();
        kv = Kv.by("userId", exist.getUserId()).set("userNickName", exist.getUserNickName())
                .set("userPhone", exist.getUserPhone() == null ? "" : exist.getUserPhone())
                .set("userEmail", exist.getUserEmail() == null ? "" : exist.getUserEmail())
                .set("userImg", exist.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken);
        return RetKit.ok("data", kv).setMsg("登录成功");
    }

    /**
     * @param token
     * @return
     * @Title: onePassLogin 一键登录
     * @Description:
     * @date 2021年10月11日 下午2:33:16
     * <AUTHOR>
     */
    public RetKit onePassLogin(String token, String platform, String ip) {
        if (StrKit.isBlank(token)) {
            return RetKit.fail("参数不能为空！");
        }
        String phone = new AliOpenapiKit().GetMobile(token);
        if (StrKit.isBlank(phone)) {
            return RetKit.fail("一键登录失败，手机号异常！");
        }

        User user = User.dao.findFirst("select * from user where userPhone=? ", phone);
        if (user == null) {
            user = new User();
            String userNickName = "User" + RandomKit.generateSixDigitCode();
            user.setUserPhone(phone).setUserNickName(userNickName).setStatus(User.STATUS_IS_LOGINED)
                    .setRegisterType(User.REGISTER_TYPE_ALI_ONES).setCreateTime(new Date());
            user.save();
        }
        // 登录有效时间 7天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
        // 过期的时间戳
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        // 生成accessToken
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());
        // 创建session
        UserSession session = new UserSession();
        session.setUserId(user.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp).setCreateTime(new Date())
                .save();
        // 记录登录时间
        user.setLastLoginTime(new Date()).update();
        // 清除账号的密码和盐
        user.removeSensitiveInfo();
        // 放进缓存
        CacheKit.put(CacheConstant.APP_USER, accessToken, user);
        // 创建登录日志
        createLoginLog(user.getUserId(), ip, Integer.valueOf(platform));
        Kv kv = new Kv();
        kv = Kv.by("userId", user.getUserId()).set("userNickName", user.getUserNickName())
                .set("userPhone", user.getUserPhone() == null ? "" : user.getUserPhone())
                .set("userEmail", user.getUserEmail() == null ? "" : user.getUserEmail())
                .set("userImg", user.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken);
        return RetKit.ok("data", kv).setMsg("登录成功");
    }

    /**
     * @param accessToken
     * @return
     * @Title: logout
     * @Description: 退出登录
     * @date 2018年12月10日 下午6:36:51
     * <AUTHOR>
     */
    public boolean logout(String accessToken) {
        if (accessToken == null) {
            return false;
        }
        boolean succ = UserSession.dao.deleteById(accessToken);
        CacheKit.remove(CacheConstant.APP_USER, accessToken);
        return succ;
    }

    public User getUserCacheWithSessionId(String accessToken) {
        return CacheKit.get(CacheConstant.APP_USER, accessToken);
    }

    public RetKit retSetPwd(String phone, String password, String checkPwd) {
        if (StrKit.isBlank(phone)) {
            return RetKit.fail("参数不能为空！");
        }
        User user = User.dao.findFirst("select * from user where userPhone=? limit 1", phone.trim());
        if (user == null) {
            return RetKit.fail("该手机号码未注册！");
        }
        if (!checkPwd.equals(password)) {
            return RetKit.fail("两次密码不一致！");
        }
        String salt = HashKit.generateSaltForSha256();
        String hashPwd = HashKit.sha256(salt + password);
        boolean succ = user.setSalt(salt).setUserPass(hashPwd).update();
        if (succ) {
            removeCacheAndSession(user.getUserId());
        }

        return succ ? RetKit.ok() : RetKit.fail();
    }

    private void createLoginLog(Integer userId, String ipAddress, Integer platform) {
        Record record = new Record();
        record.set("userId", userId).set("ip", ipAddress).set("platform", platform).set("loginTime", new Date());
        Db.save("user_login_log", record);
    }

    private void removeCacheAndSession(Integer userId) {
        List<UserSession> list = UserSession.dao.find("select * from user_session where userId=?", userId);
        for (UserSession cus : list) {
            CacheKit.remove(CacheConstant.APP_USER, cus.getSessionId());
        }
        Db.update("delete from user_session where userId=?", userId);
    }

    public User loginWithSessionId(String sessionId, String ipAddress, Integer platform) {
        UserSession session = UserSession.dao.findById(sessionId);
        if (session == null) {
            return null;
        }
        // 判断sessionId是否已过期，如果过期则删除
        if (session.isExpired()) {
            session.delete();
            return null;
        }

        User user = User.dao.findById(session.getUserId());
        if (user != null) {
            user.setLastLoginTime(new Date()).update();
            user.removeSensitiveInfo();
            user.put("sessionId", sessionId);
            CacheKit.put(CacheConstant.APP_USER, sessionId, user);
            createLoginLog(user.getUserId(), ipAddress, platform);
            return user;
        }

        return null;
    }

    /**
     * 安全的用户注销方法
     * 包含二次验证、操作日志记录、数据备份等安全机制
     *
     * @param accessToken 访问令牌
     * @param userId      确认码（通过短信/邮件发送）
     * @param reason      注销原因
     * @param ipAddress   IP地址
     * @return 操作结果
     */
    public RetKit unregisterWithSecurity(String accessToken, String userId, String reason, String ipAddress) {
        LogKit.warn("执行安全注销验证 - IP: " + ipAddress);

        // 1. 基本参数验证
        if (StrKit.isBlank(accessToken)) {
            return RetKit.fail("未登录！");
        }

        // 2. 验证会话有效性并获取用户ID
        UserSession session = UserSession.dao.findById(accessToken);
        if (session == null) {
            LogKit.warn("注销失败：会话不存在 - IP: " + ipAddress);
            return RetKit.fail("未登录或会话已过期！");
        }

        // 3. 从session中获取userId
        Integer sessionUserId = session.getUserId();
        LogKit.warn("用户注销验证 - UserId: " + userId + ", IP: " + ipAddress);
        // 判断sessionUserId是否等于userId
        if (sessionUserId == null || !sessionUserId.equals(Integer.valueOf(userId))) {
            LogKit.warn("注销失败：用户ID不匹配 - UserId: " + userId + ", IP: " + ipAddress);
            return RetKit.fail("错误的用户ID！");
        }

        // 4. 获取用户完整信息用于备份和验证
        User user = User.dao.findById(userId);
        if (user == null) {
            return RetKit.fail("用户不存在！");
        }

        String phone = user.getUserPhone();
        String email = user.getUserEmail();
        if (StrKit.isBlank(phone) && StrKit.isBlank(email)) {
            return RetKit.fail("用户未绑定手机号或邮箱，无法验证身份！");
        }

        try {
            // 6. 事务处理：备份数据并执行注销
            Db.tx(() -> {
                // 6.1 创建用户数据备份记录
                createUserBackupRecord(user, reason, ipAddress);

                // 6.2 删除用户数据（已有备份，直接删除）
                softDeleteUserData(user);

                // 6.3 记录注销操作日志
                createUnregisterLog(sessionUserId, reason, ipAddress, "SUCCESS");

                return true;
            });

            // 7. 清除所有相关会话和缓存
            logout(accessToken);
            removeCacheAndSession(sessionUserId);

            // 8. 清除确认码
            CacheKit.remove(CacheConstant.CAPTCHA, phone);

            // 9. 注销后发送邮件通知
            if (StrKit.notBlank(email)) {
                String subject = "账户注销通知";
                String content = "尊敬的用户，您的账户已成功注销。如有疑问请联系客服。";
                EmailKit.sendMail(email, content, subject);
            }

            LogKit.warn("用户注销成功 - UserId: " + userId + ", IP: " + ipAddress + ", Reason: " + reason);
            return RetKit.ok("注销成功");

        } catch (Exception e) {
            LogKit.error("用户注销失败 - UserId: " + userId + ", IP: " + ipAddress, e);
            // 记录失败日志
            createUnregisterLog(sessionUserId, reason, ipAddress, "FAILED: " + e.getMessage());
            return RetKit.fail("注销失败，请稍后重试！");
        }
    }

    /**
     * 删除用户数据（直接删除，因为已有备份）
     */
    private void softDeleteUserData(User user) {
        if (user == null) {
            return;
        }
        Integer userId = user.getUserId();

        // 将用户账号加入黑名单，设置7天过期时间
        long expireTime = System.currentTimeMillis() + (CacheConstant.BLACKLIST_EXPIRE_DAYS * 24 * 60 * 60 * 1000L);

        // 如果有手机号，将手机号加入黑名单
        if (StrKit.notBlank(user.getUserPhone())) {
            CacheKit.put(CacheConstant.BLACKLIST, user.getUserPhone(), expireTime);
        }

        // 如果有邮箱，将邮箱加入黑名单
        if (StrKit.notBlank(user.getUserEmail())) {
            CacheKit.put(CacheConstant.BLACKLIST, user.getUserEmail().toLowerCase(), expireTime);
        }

        // 直接删除用户记录，因为已经在user_backup中备份了完整数据
        Db.update("delete from user where userId = ?", userId);

        // 软删除相关数据（这些表可能还需要保留用于其他业务逻辑）
        Db.update("update templet set isDel = 1, deleteTime = ? where userId = ?", new Date(), userId);
        Db.update("update templet_group set isDel = 1, deleteTime = ? where userId = ?", new Date(), userId);
        Db.update("update feedback set isDel = 1, deleteTime = ? where userId = ?", new Date(), userId);
    }

    /**
     * 检查账号是否在黑名单中
     */
    private boolean isAccountInBlacklist(String account) {
        if (StrKit.isBlank(account)) {
            return false;
        }

        // 如果是邮箱，统一转小写
        if (account.contains("@")) {
            account = account.toLowerCase();
        }

        Object expireTime = CacheKit.get(CacheConstant.BLACKLIST, account);
        if (expireTime == null) {
            return false;
        }

        // 如果黑名单已过期，移除黑名单记录
        if (System.currentTimeMillis() > (Long) expireTime) {
            CacheKit.remove(CacheConstant.BLACKLIST, account);
            return false;
        }

        return true;
    }

    /**
     * 创建用户数据备份记录
     */
    private void createUserBackupRecord(User user, String reason, String ipAddress) {
        Record backupRecord = new Record();
        backupRecord.set("userId", user.getUserId())
                .set("userData", user.toJson()) // 备份用户完整数据
                .set("reason", reason)
                .set("ipAddress", ipAddress)
                .set("backupTime", new Date())
                .set("expireTime", new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000)); // 30天过期

        Db.save("user_backup", backupRecord);
    }

    /**
     * 创建注销操作日志
     */
    private void createUnregisterLog(Integer userId, String reason, String ipAddress, String status) {
        Record logRecord = new Record();
        logRecord.set("userId", userId)
                .set("reason", reason)
                .set("ipAddress", ipAddress)
                .set("status", status)
                .set("operateTime", new Date());

        Db.save("user_unregister_log", logRecord);
    }

    /**
     * 邮箱验证码登录
     *
     * @param email     邮箱地址
     * @param captcha   验证码
     * @param platform  平台信息
     * @param ipAddress IP地址
     * @return 登录结果
     */
    public RetKit emailCaptchaLogin(String email, String captcha, String platform, String ipAddress) {
        if (StrKit.isBlank(email) || StrKit.isBlank(captcha)) {
            return RetKit.fail("邮箱和验证码不能为空！");
        }
        email = email.trim().toLowerCase(); // 邮箱统一转小写

        // 检查邮箱是否在黑名单中
        if (isAccountInBlacklist(email)) {
            return RetKit.fail("该账号已被注销，7天内不能重新注册登录！");
        }

        // 1. 检查尝试次数和锁定状态
        String attemptKey = CacheConstant.CAPTCHA_ATTEMPT + email;
        Object attemptValue = CacheKit.get(CacheConstant.CAPTCHA, attemptKey);

        // 2. 检查是否被锁定
        if (CacheConstant.CAPTCHA_LOCKED_VALUE.equals(attemptValue)) {
            return RetKit.fail("尝试次数过多，请稍后再试！");
        }

        // 3. 获取当前尝试次数
        int attemptCount = 0;
        if (attemptValue instanceof Integer) {
            attemptCount = (Integer) attemptValue;
        }

        // 4. 验证码校验
        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, email);
        if (realCaptcha == null) {
            return RetKit.fail("验证码已过期，请重新发送验证码！");
        }
        if (!captcha.equals(realCaptcha)) {
            // 增加尝试次数
            int maxAttempts = 3; // 最大尝试次数
            attemptCount++;
            if (attemptCount >= maxAttempts) {
                // 达到最大尝试次数，锁定
                CacheKit.put(CacheConstant.CAPTCHA, attemptKey, CacheConstant.CAPTCHA_LOCKED_VALUE);
                return RetKit.fail("尝试次数过多，请稍后再试！");
            } else {
                // 记录尝试次数
                CacheKit.put(CacheConstant.CAPTCHA, attemptKey, attemptCount);
                return RetKit.fail("验证码错误！");
            }
        }

        // 5. 验证码正确，清除尝试次数和验证码缓存
        CacheKit.remove(CacheConstant.CAPTCHA, attemptKey);
        CacheKit.remove(CacheConstant.CAPTCHA, email);

        // 6. 查找或创建用户
        User checkUser = User.dao.findFirst("select * from user where userEmail=?", email);
        if (checkUser != null) {
            // 检查用户是否在黑名单中
            if (isAccountInBlacklist(email)) {
                return RetKit.fail("该账号已被注销，7天内不能重新注册登录！");
            }
        } else {
            // 创建用户
            String userNickName = "User" + RandomKit.generateSixDigitCode();
            checkUser = new User();
            checkUser.setUserEmail(email).setUserNickName(userNickName).setStatus(User.STATUS_IS_REGISTER)
                    .setCreateTime(new Date());
            try {
                boolean succ = checkUser.save();
                if (!succ) {
                    return RetKit.fail("登录失败！");
                }
            } catch (Exception ex) {
                LogKit.error("第三方登录失败：" + ex.getMessage());
                return RetKit.fail("登录失败！");
            }
        }

        // 7. 创建登录会话
        // 登录有效时间 30天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());

        UserSession session = new UserSession();
        session.setUserId(checkUser.getUserId())
                .setSessionId(accessToken)
                .setTimeStamp(timeStamp)
                .setCreateTime(new Date())
                .save();

        // 8. 更新用户最后登录时间
        checkUser.setLastLoginTime(new Date()).update();

        // 9. 清除敏感信息并放入缓存
        checkUser.removeSensitiveInfo();
        CacheKit.put(CacheConstant.APP_USER, accessToken, checkUser);

        // 10. 创建登录日志
        createLoginLog(checkUser.getUserId(), ipAddress, Integer.valueOf(platform));

        // 11. 返回登录结果
        Kv kv = Kv.by("userId", checkUser.getUserId())
                .set("userNickName", checkUser.getUserNickName())
                .set("userEmail", email)
                .set("userImg", checkUser.getUserImg())
                .set(Constant.APP_ACCESSTOKE, accessToken);

        return RetKit.ok("data", kv).setMsg("登录成功");
    }

}
