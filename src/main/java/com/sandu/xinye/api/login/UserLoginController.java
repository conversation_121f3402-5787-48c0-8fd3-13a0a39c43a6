package com.sandu.xinye.api.login;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;

/**
 * 用户登录控制器
 * 提供用户注册、登录、注销、退出等功能
 */
public class UserLoginController extends AppController {

    /**
     * 用户注销接口
     * 注销用户账号并删除所有相关数据
     * 注意：这是一个高风险操作，需要严格的安全验证
     * <p>
     * 使用说明：
     * 1. 先调用 /api/common/sendCaptcha 发送注销验证码
     * 参数: phone=手机号, type=5 (注销类型)
     * 2. 再调用此接口进行注销
     * Headers: accessToken=用户令牌
     * 参数: confirmCode=验证码, reason=注销原因(可选)
     * <p>
     * 安全机制：
     * - 验证用户身份和会话有效性
     * - 软删除机制（数据保留30天）
     * - 完整的操作日志记录
     * - 用户数据自动备份
     */
    public void unregister() {
        LogKit.info("用户注销接口开始--------------------");
        try {
            String accessToken = getHeader(Constant.APP_ACCESSTOKE);
            String userId = getPara("userId"); // userId
            String reason = getPara("reason"); // 注销原因（可选）
            String ipAddress = getIpAddress();

            // 参数验证
            if (StrKit.isBlank(accessToken)) {
                renderJson(RetKit.fail("访问令牌不能为空！"));
                return;
            }

            // 记录高风险操作日志
            LogKit.warn("用户注销请求 - IP: " + ipAddress + ", Reason: " + reason);

            RetKit ret = UserLoginService.me.unregisterWithSecurity(accessToken, userId, reason, ipAddress);

            // 记录操作结果
            if (ret.success()) {
                LogKit.warn("用户注销成功 - IP: " + ipAddress);
            } else {
                LogKit.warn("用户注销失败 - IP: " + ipAddress + ", Error: " + ret.get("msg"));
            }

            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("用户注销接口异常", e);
            renderJson(RetKit.fail("注销失败，系统异常！"));
        } finally {
            LogKit.info("用户注销接口结束--------------------");
        }
    }

    /**
     * 短信验证码登录接口
     * 通过手机号和验证码进行登录
     */
    @Clear
    @Before({I18nInterceptor.class})
    public void captchaLogin() {
        LogKit.info("用户验证码登录接口开始--------------------");
        try {
            String phone = getPara("phone");
            String captcha = getPara("captcha");
            String platform = getHeader("platform");

            // 参数验证
            if (StrKit.isBlank(phone)) {
                renderJson(RetKit.fail("手机号不能为空！"));
                return;
            }

            if (StrKit.isBlank(captcha)) {
                renderJson(RetKit.fail("验证码不能为空！"));
                return;
            }

            if (StrKit.isBlank(platform)) {
                renderJson(RetKit.fail("平台信息不能为空！"));
                return;
            }

            // 手机号格式简单验证
            phone = phone.trim();
            if (!isValidPhoneNumber(phone)) {
                renderJson(RetKit.fail("手机号格式不正确！"));
                return;
            }

            RetKit ret = UserLoginService.me.captchaLogin(phone, captcha, platform, getIpAddress());
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("用户验证码登录接口异常", e);
            renderJson(RetKit.fail("登录失败！"));
        } finally {
            LogKit.info("用户验证码登录接口结束--------------------");
        }
    }

    /**
     * 邮箱验证码登录接口
     * 通过邮箱和验证码进行登录
     * <p>
     * 使用说明：
     * 1. 先调用 /api/common/sendEmailCaptcha 发送邮箱验证码
     * 参数: email=邮箱地址, type=3 (登录类型) headers: Accept-Language=语言环境(可选)
     * 2. 再调用此接口进行登录
     * Headers: platform=平台信息 0 - android 1 - ios
     * 参数: email=邮箱地址, captcha=验证码
     * <p>
     * 注意事项：
     * - 邮箱格式会自动转换为小写
     * - 如果邮箱未注册，系统会自动创建新用户
     * - 用户昵称默认使用邮箱前缀
     * - 验证码错误超过3次会被锁定
     */
    @Clear
    @Before({I18nInterceptor.class})
    public void emailCaptchaLogin() {
        LogKit.info("邮箱验证码登录接口开始--------------------");
        try {
            String email = getPara("email");
            String captcha = getPara("captcha");
            String platform = getHeader("platform");

            // 参数验证
            if (StrKit.isBlank(email)) {
                renderJson(RetKit.fail("邮箱不能为空！"));
                return;
            }

            if (StrKit.isBlank(captcha)) {
                renderJson(RetKit.fail("验证码不能为空！"));
                return;
            }

            if (StrKit.isBlank(platform)) {
                renderJson(RetKit.fail("平台信息不能为空！"));
                return;
            }

            // 邮箱格式验证
            email = email.trim().toLowerCase();
            if (!isValidEmail(email)) {
                renderJson(RetKit.fail("邮箱格式不正确！"));
                return;
            }

            RetKit ret = UserLoginService.me.emailCaptchaLogin(email, captcha, platform, getIpAddress());
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("邮箱验证码登录接口异常", e);
            renderJson(RetKit.fail("登录失败！"));
        } finally {
            LogKit.info("邮箱验证码登录接口结束--------------------");
        }
    }

    /**
     * 一键登录接口
     * 通过第三方平台提供的accessToken进行快速登录
     */
    @Clear
    @Before({I18nInterceptor.class})
    public void onePassLogin() {
        LogKit.info("一键登录接口开始--------------------");
        try {
            String accessToken = getPara("accessToken");
            String platform = getHeader("platform");

            // 参数验证
            if (StrKit.isBlank(accessToken)) {
                renderJson(RetKit.fail("访问令牌不能为空！"));
                return;
            }

            if (StrKit.isBlank(platform)) {
                renderJson(RetKit.fail("平台信息不能为空！"));
                return;
            }

            RetKit ret = UserLoginService.me.onePassLogin(accessToken, platform, getIpAddress());
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("一键登录接口异常", e);
            renderJson(RetKit.fail("登录失败！"));
        } finally {
            LogKit.info("一键登录接口结束--------------------");
        }
    }

    /**
     * 退出登录接口
     * 清除用户session和缓存
     */
    public void logout() {
        LogKit.info("退出登录接口开始--------------------");
        try {
            String accessToken = getHeader(Constant.APP_ACCESSTOKE);

            // 参数验证
            if (StrKit.isBlank(accessToken)) {
                renderJson(RetKit.fail("访问令牌不能为空！"));
                return;
            }

            boolean success = UserLoginService.me.logout(accessToken);
            renderJson(success ? RetKit.ok("退出登录成功") : RetKit.fail("退出登录失败"));

        } catch (Exception e) {
            LogKit.error("退出登录接口异常", e);
            renderJson(RetKit.fail("退出登录失败，系统异常！"));
        } finally {
            LogKit.info("退出登录接口结束--------------------");
        }
    }

    /**
     * 验证手机号格式
     *
     * @param phone 手机号
     * @return 是否有效
     */
    private boolean isValidPhoneNumber(String phone) {
        if (StrKit.isBlank(phone)) {
            return false;
        }
        // 简单的手机号验证（支持国际号码）
        return phone.matches("^[+]?[0-9]{10,15}$");
    }

    /**
     * 验证邮箱格式
     *
     * @param email 邮箱地址
     * @return 是否有效
     */
    private boolean isValidEmail(String email) {
        if (StrKit.isBlank(email)) {
            return false;
        }
        // 简单的邮箱格式验证
        return email.matches("^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }

    // 以下是被注释的接口，保留原始注释状态
    // @Clear
    // public void register() {
    // LogKit.info("用户注册接口开始--------------------");
    // String phone = getPara("phone");
    // String password = getPara("password");
    // String captcha = getPara("captcha");
    // RetKit ret = UserLoginService.me.register(phone, password, captcha,
    // getIpAddress());
    // renderJson(ret);
    // LogKit.info("用户注册接口结束--------------------");
    // }

    // @Clear
    // public void doLogin() {
    // LogKit.info("用户账号登录接口开始--------------------");
    // String phone = getPara("phone");
    // String password = getPara("password");
    // String platform = getHeader("platform");
    // RetKit ret = UserLoginService.me.doLogin(phone, password, platform,
    // getIpAddress());
    // renderJson(ret);
    // LogKit.info("用户账号登录接口结束--------------------");
    // }

    // @Clear
    // public void retSetPwd() {
    // LogKit.info("调用忘记、重设密码接口开始-------------------------->" +
    // getParaToMap().toJson());
    // String phone = getPara("phone");
    // String password = getPara("password");
    // String checkPwd = getPara("checkPwd");
    // RetKit ret = UserLoginService.me.retSetPwd(phone, password, checkPwd);
    // renderJson(ret);
    // LogKit.info("调用忘记、重设密码接口结束-------------------------->");
    // }

    // @Clear
    // public void fasterLogin() {
    // LogKit.info("调用第三方登录接口开始---------------");
    // String openId = getPara("openId");
    // Integer loginType = getParaToInt("loginType");
    // String platform = getHeader("platform");
    // String avatar = getPara("avatar");
    // String nickName = getPara("nickName");
    // String ip = getIpAddress();
    // RetKit ret = UserLoginService.me.fasterLogin(openId, loginType, platform,
    // avatar, nickName, ip);
    // renderJson(ret);
    // LogKit.info("调用第三方登录接口结束---------------");
    // }

    // @Clear
    // public void appleLogin() {
    // String appleUserId = getPara("appleUserId");
    // String platform = getHeader("platform");
    // String ip = getIpAddress();
    // int loginType = User.REGISTER_TYPE_APPLE_USER;
    // RetKit ret = UserLoginService.me.appleLogin(appleUserId, loginType, platform,
    // ip);
    // renderJson(ret);
    // }

}