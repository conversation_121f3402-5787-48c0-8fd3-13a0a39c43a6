package com.sandu.xinye.api.user;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;

/**
 * 用户绑定控制器
 * 提供用户绑定手机号、邮箱等功能
 */
public class UserController extends AppController {

    /**
     * 绑定手机号接口
     * 通过验证码绑定手机号到当前用户
     * <p>
     * 使用说明：
     * 1. 先调用 /api/common/sendCaptcha 发送验证码
     * 参数: phone=手机号, type=7 (绑定手机号类型)
     * 2. 再调用此接口进行绑定
     * <p>
     * 参数:
     * - phone: 手机号
     * - captcha: 验证码
     * <p>
     * Headers: accessToken=用户令牌
     */
    @OperationLog(modelName = "userBind")
    public void bindPhone() {
        LogKit.info("绑定手机号接口开始--------------------");
        try {
            String accessToken = getHeader(Constant.APP_ACCESSTOKE);
            String phone = getPara("phone");
            String captcha = getPara("captcha");

            if (StrKit.isBlank(phone)) {
                renderJson(RetKit.fail("手机号不能为空！"));
                return;
            }

            if (StrKit.isBlank(captcha)) {
                renderJson(RetKit.fail("验证码不能为空！"));
                return;
            }

            // 手机号格式验证
            phone = phone.trim();
            if (!isValidPhoneNumber(phone)) {
                renderJson(RetKit.fail("手机号格式不正确！"));
                return;
            }

            RetKit ret = UserService.me.bindPhone(accessToken, phone, captcha, getIpAddress());
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("绑定手机号接口异常", e);
            renderJson(RetKit.fail("绑定手机号失败！"));
        } finally {
            LogKit.info("绑定手机号接口结束--------------------");
        }
    }

    /**
     * 绑定邮箱接口
     * 通过验证码绑定邮箱到当前用户
     * <p>
     * 使用说明：
     * 1. 先调用 /api/common/sendEmailCaptcha 发送验证码
     * 参数: email=邮箱地址, type=8 (绑定邮箱类型)
     * 2. 再调用此接口进行绑定
     * <p>
     * 参数:
     * - email: 邮箱地址
     * - captcha: 验证码
     * <p>
     * Headers: accessToken=用户令牌
     */
    @OperationLog(modelName = "userBind")
    public void bindEmail() {
        LogKit.info("绑定邮箱接口开始--------------------");
        try {
            String accessToken = getHeader(Constant.APP_ACCESSTOKE);
            String email = getPara("email");
            String captcha = getPara("captcha");

            if (StrKit.isBlank(accessToken)) {
                renderJson(RetKit.fail("请先登录！"));
                return;
            }

            if (StrKit.isBlank(email)) {
                renderJson(RetKit.fail("邮箱不能为空！"));
                return;
            }

            if (StrKit.isBlank(captcha)) {
                renderJson(RetKit.fail("验证码不能为空！"));
                return;
            }

            // 邮箱格式验证
            email = email.trim().toLowerCase();
            if (!isValidEmail(email)) {
                renderJson(RetKit.fail("邮箱格式不正确！"));
                return;
            }

            RetKit ret = UserService.me.bindEmail(accessToken, email, captcha, getIpAddress());
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("绑定邮箱接口异常", e);
            renderJson(RetKit.fail("绑定邮箱失败！"));
        } finally {
            LogKit.info("绑定邮箱接口结束--------------------");
        }
    }

    /**
     * 验证原绑定信息接口
     * 验证用户原来已绑定的手机号或邮箱
     * <p>
     * 使用说明：
     * 1. 调用 /api/common/sendCaptcha 或 /api/common/sendEmailCaptcha 发送验证码到原绑定目标
     * 参数: phone/email=原绑定信息
     * 2. 再调用此接口进行验证
     * <p>
     * 参数:
     * - phone: 原绑定手机号（验证手机号时必填）
     * - email: 原绑定邮箱（验证邮箱时必填）
     * - captcha: 验证码
     * <p>
     * Headers: accessToken=用户令牌
     * <p>
     * 返回数据：
     * {
     * "code": 200,
     * "msg": "验证成功"
     * }
     */
    public void verifyBind() {
        LogKit.info("验证原绑定信息接口开始--------------------");
        try {
            String accessToken = getHeader(Constant.APP_ACCESSTOKE);
            String phone = getPara("phone");
            String email = getPara("email");
            String captcha = getPara("captcha");

            if (StrKit.isBlank(captcha)) {
                renderJson(RetKit.fail("验证码不能为空！"));
                return;
            }

            RetKit ret = UserService.me.verifyOriginalBind(accessToken, phone, email, captcha, getIpAddress());
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("验证原绑定信息接口异常", e);
            renderJson(RetKit.fail("验证失败！"));
        } finally {
            LogKit.info("验证原绑定信息接口结束--------------------");
        }
    }

    /**
     * 获取用户信息接口
     * 获取当前登录用户的基本信息，包括头像、昵称等
     * <p>
     * Headers: accessToken=用户令牌
     * <p>
     * 返回数据：
     * {
     * "code": 200,
     * "msg": "获取成功",
     * "data": {
     * "userId": 123,
     * "userNickName": "用户昵称",
     * "avatarUrl": "https://cdn.example.com/img/tattoogo/userId/xxx.jpg",
     * "hasPhone": true,
     * "hasEmail": false,
     * "phone": "13812345678",
     * "email": "<EMAIL>"
     * }
     * }
     */
    public void info() {
        LogKit.info("获取用户信息接口开始--------------------");
        try {
            String accessToken = getHeader(Constant.APP_ACCESSTOKE);

            if (StrKit.isBlank(accessToken)) {
                renderJson(RetKit.fail("请先登录！"));
                return;
            }

            RetKit ret = UserService.me.getUserInfo(accessToken);
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("获取用户信息接口异常", e);
            renderJson(RetKit.fail("获取用户信息失败！"));
        } finally {
            LogKit.info("获取用户信息接口结束--------------------");
        }
    }

    /**
     * 修改用户头像接口
     * 通过阿里云OSS上传用户头像
     * <p>
     * 使用说明：
     * 1. 使用multipart/form-data格式上传文件
     * 2. 文件字段名为 avatar
     * 3. 支持的文件格式：jpg、jpeg、png、bmp
     * 4. 文件大小限制：最大10MB
     * <p>
     * Headers: accessToken=用户令牌
     * <p>
     * 返回数据：
     * {
     * "code": 200,
     * "msg": "头像修改成功",
     * "data": {
     * "avatarUrl": "https://cdn.example.com/img/tattoogo/userId/xxx.jpg"
     * }
     * }
     */
    @OperationLog(modelName = "userAvatar")
    @Before({ PostOnlyInterceptor.class })
    public void updateAvatar() {
        LogKit.info("修改用户头像接口开始--------------------");
        try {
            String accessToken = getHeader(Constant.APP_ACCESSTOKE);
            // 获取上传的文件
            UploadFile avatarFile = getFile("avatar");
            if (avatarFile == null) {
                renderJson(RetKit.fail("请选择要上传的头像文件！"));
                return;
            }

            RetKit ret = UserService.me.updateAvatar(accessToken, avatarFile, getIpAddress());
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("修改用户头像接口异常", e);
            renderJson(RetKit.fail("头像修改失败！"));
        } finally {
            LogKit.info("修改用户头像接口结束--------------------");
        }
    }

    /**
     * 修改用户昵称接口
     * <p>
     * 参数:
     * - nickname: 新的昵称
     * <p>
     * Headers: accessToken=用户令牌
     * <p>
     * 返回数据：
     * {
     * "code": 200,
     * "msg": "昵称修改成功"
     * }
     */
    @OperationLog(modelName = "userNickname")
    @Before({ PostOnlyInterceptor.class })
    public void updateNickname() {
        LogKit.info("修改用户昵称接口开始--------------------");
        try {
            String accessToken = getHeader(Constant.APP_ACCESSTOKE);
            String nickname = getPara("nickname");

            if (StrKit.isBlank(accessToken)) {
                renderJson(RetKit.fail("请先登录！"));
                return;
            }
            if (StrKit.isBlank(nickname)) {
                renderJson(RetKit.fail("昵称不能为空！"));
                return;
            }
            // 可根据需要增加昵称长度、敏感词等校验

            RetKit ret = UserService.me.updateNickname(accessToken, nickname, getIpAddress());
            renderJson(ret);

        } catch (Exception e) {
            LogKit.error("修改用户昵称接口异常", e);
            renderJson(RetKit.fail("昵称修改失败！"));
        } finally {
            LogKit.info("修改用户昵称接口结束--------------------");
        }
    }

    /**
     * 验证手机号格式
     */
    private boolean isValidPhoneNumber(String phone) {
        if (StrKit.isBlank(phone)) {
            return false;
        }
        // 简单的手机号格式验证，支持国内和国际号码
        return phone.matches("^[1-9]\\d{10}$") || phone.matches("^\\+\\d{10,15}$");
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        if (StrKit.isBlank(email)) {
            return false;
        }
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }
}