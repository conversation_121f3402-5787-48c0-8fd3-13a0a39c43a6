package com.sandu.xinye.api.user;

import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.api.oss.OssService;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.UserSession;

/**
 * 用户绑定服务类
 * 处理用户绑定手机号、邮箱等业务逻辑
 */
public class UserService {

    public static final UserService me = new UserService();

    /**
     * 获取用户绑定状态
     *
     * @param accessToken 用户访问令牌
     * @return 绑定状态信息
     */
    public RetKit getBindStatus(String accessToken) {
        try {
            // 验证用户会话
            User user = getUserByAccessToken(accessToken);
            if (user == null) {
                return RetKit.fail("未登录！");
            }

            String phone = user.getUserPhone();
            String email = user.getUserEmail();

            // 构建返回数据
            Kv data = Kv.create();
            data.set("hasPhone", StrKit.notBlank(phone));
            data.set("hasEmail", StrKit.notBlank(email));

            // 脱敏显示手机号
            if (StrKit.notBlank(phone)) {
                data.set("phone", maskPhone(phone));
            } else {
                data.set("phone", "");
            }

            // 脱敏显示邮箱
            if (StrKit.notBlank(email)) {
                data.set("email", maskEmail(email));
            } else {
                data.set("email", "");
            }

            return RetKit.ok("data", data);

        } catch (Exception e) {
            LogKit.error("获取用户绑定状态异常", e);
            return RetKit.fail("获取绑定状态失败！");
        }
    }

    /**
     * 绑定手机号
     *
     * @param accessToken 用户访问令牌
     * @param phone       手机号
     * @param captcha     验证码
     * @param ipAddress   IP地址
     * @return 绑定结果
     */
    public RetKit bindPhone(String accessToken, String phone, String captcha, String ipAddress) {
        try {
            // 验证用户会话
            User user = getUserByAccessToken(accessToken);
            if (user == null) {
                return RetKit.fail("未登录！");
            }

            // 验证验证码
            String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
            if (realCaptcha == null) {
                return RetKit.fail("验证码已过期，请重新发送验证码！");
            }
            if (!captcha.equals(realCaptcha)) {
                return RetKit.fail("验证码错误！");
            }

            // 检查手机号是否已被其他用户绑定
            User existUser = User.dao.findFirst("SELECT * FROM user WHERE userPhone = ? AND userId != ?", phone,
                    user.getUserId());
            if (existUser != null) {
                return RetKit.fail("该手机号已被其他用户绑定！");
            }

            // 更新用户手机号
            boolean success = user.setUserPhone(phone).update();
            if (success) {
                // 清除验证码缓存
                CacheKit.remove(CacheConstant.CAPTCHA, phone);

                LogKit.info("用户绑定手机号成功 - userId: " + user.getUserId() + ", phone: " + maskPhone(phone));
                return RetKit.ok().setMsg("绑定手机号成功！");
            } else {
                return RetKit.fail("绑定手机号失败！");
            }

        } catch (Exception e) {
            LogKit.error("绑定手机号异常", e);
            return RetKit.fail("绑定手机号失败！");
        }
    }

    /**
     * 绑定邮箱
     *
     * @param accessToken 用户访问令牌
     * @param email       邮箱
     * @param captcha     验证码
     * @param ipAddress   IP地址
     * @return 绑定结果
     */
    public RetKit bindEmail(String accessToken, String email, String captcha, String ipAddress) {
        try {
            // 验证用户会话
            User user = getUserByAccessToken(accessToken);
            if (user == null) {
                return RetKit.fail("未登录！");
            }

            // 验证验证码
            String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, email);
            if (realCaptcha == null) {
                return RetKit.fail("验证码已过期，请重新发送验证码！");
            }
            if (!captcha.equals(realCaptcha)) {
                return RetKit.fail("验证码错误！");
            }

            // 检查邮箱是否已被其他用户绑定
            User existUser = User.dao.findFirst("SELECT * FROM user WHERE userEmail = ? AND userId != ?", email,
                    user.getUserId());
            if (existUser != null) {
                return RetKit.fail("该邮箱已注册或已绑定！");
            }

            // 更新用户邮箱
            boolean success = user.setUserEmail(email).update();
            if (success) {
                // 清除验证码缓存
                CacheKit.remove(CacheConstant.CAPTCHA, email);

                LogKit.info("用户绑定邮箱成功 - userId: " + user.getUserId() + ", email: " + maskEmail(email));
                return RetKit.ok().setMsg("绑定邮箱成功！");
            } else {
                return RetKit.fail("绑定邮箱失败！");
            }

        } catch (Exception e) {
            LogKit.error("绑定邮箱异常", e);
            return RetKit.fail("绑定邮箱失败！");
        }
    }

    /**
     * 验证原绑定信息
     *
     * @param accessToken 用户访问令牌
     * @param phone       原绑定手机号
     * @param email       原绑定邮箱
     * @param captcha     验证码
     * @param ipAddress   IP地址
     * @return 验证结果
     */
    public RetKit verifyOriginalBind(String accessToken, String phone, String email, String captcha,
            String ipAddress) {
        try {
            // 验证用户会话
            User user = getUserByAccessToken(accessToken);
            if (user == null) {
                return RetKit.fail("未登录！");
            }

            String target = null;
            String userBound = null;

            // 如果手机号不为空，说明是验证手机号
            if (StrKit.notBlank(phone)) {
                if (StrKit.isBlank(phone)) {
                    return RetKit.fail("手机号不能为空！");
                }
                target = phone;
                userBound = user.getUserPhone();

                if (StrKit.isBlank(userBound)) {
                    return RetKit.fail("您还未绑定手机号！");
                }

                if (!phone.equals(userBound)) {
                    return RetKit.fail("手机号与已绑定的不一致！");
                }

            } else if (StrKit.notBlank(email)) {
                // 如果邮箱不为空，说明是验证邮箱
                target = email;
                userBound = user.getUserEmail();

                if (StrKit.isBlank(userBound)) {
                    return RetKit.fail("您还未绑定邮箱！");
                }

                if (!email.equals(userBound)) {
                    return RetKit.fail("邮箱与已绑定的不一致！");
                }

            } else {
                return RetKit.fail("不支持的验证类型！");
            }

            // 验证验证码
            String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, target);
            if (realCaptcha == null) {
                return RetKit.fail("验证码已过期，请重新发送验证码！");
            }
            if (!captcha.equals(realCaptcha)) {
                return RetKit.fail("验证码错误！");
            }

            // 清除验证码缓存
            CacheKit.remove(CacheConstant.CAPTCHA, target);

            return RetKit.ok().setMsg("验证成功！");

        } catch (Exception e) {
            LogKit.error("验证原绑定信息异常", e);
            return RetKit.fail("验证失败！");
        }
    }

    /**
     * 根据访问令牌获取用户信息
     */
    private User getUserByAccessToken(String accessToken) {
        if (StrKit.isBlank(accessToken)) {
            return null;
        }

        UserSession session = UserSession.dao.findById(accessToken);
        if (session == null) {
            return null;
        }

        // 检查会话是否过期
        if (session.getTimeStamp() < System.currentTimeMillis()) {
            return null;
        }

        return User.dao.findById(session.getUserId());
    }

    /**
     * 脱敏显示手机号
     */
    private String maskPhone(String phone) {
        if (StrKit.isBlank(phone) || phone.length() < 7) {
            return phone;
        }

        if (phone.startsWith("+")) {
            // 国际号码
            if (phone.length() > 8) {
                return phone.substring(0, phone.length() - 8) + "****" + phone.substring(phone.length() - 4);
            }
        } else {
            // 国内号码
            if (phone.length() == 11) {
                return phone.substring(0, 3) + "****" + phone.substring(7);
            }
        }

        return phone;
    }

    /**
     * 脱敏显示邮箱
     */
    private String maskEmail(String email) {
        if (StrKit.isBlank(email) || !email.contains("@")) {
            return email;
        }

        String[] parts = email.split("@");
        if (parts.length != 2) {
            return email;
        }

        String username = parts[0];
        String domain = parts[1];

        if (username.length() <= 2) {
            return username + "****@" + domain;
        } else if (username.length() <= 4) {
            return username.substring(0, 1) + "****@" + domain;
        } else {
            return username.substring(0, 2) + "****@" + domain;
        }
    }

    /**
     * 修改用户头像
     *
     * @param accessToken 用户访问令牌
     * @param avatarFile  头像文件
     * @param ipAddress   IP地址
     * @return 修改结果
     */
    public RetKit updateAvatar(String accessToken, UploadFile avatarFile, String ipAddress) {
        try {
            // 验证用户会话
            User user = getUserByAccessToken(accessToken);
            if (user == null) {
                return RetKit.fail("未登录！");
            }

            // 使用OSS服务上传头像
            RetKit uploadResult = OssService.me.uploadImg(avatarFile, user.getUserId().toString());
            if (!uploadResult.success()) {
                return uploadResult; // 直接返回上传失败的结果
            }

            // 获取上传后的头像URL
            String avatarUrl = (String) uploadResult.get("data");
            if (StrKit.isBlank(avatarUrl)) {
                return RetKit.fail("头像上传失败，未获取到有效URL！");
            }

            // 保存旧头像URL（用于可能的回滚或清理）
            String oldAvatarUrl = user.getUserImg();

            // 更新用户头像
            boolean success = user.setUserImg(avatarUrl).update();
            if (success) {
                LogKit.info("用户头像修改成功 - userId: " + user.getUserId() + ", newAvatar: " + avatarUrl);

                // 构建返回数据
                Kv data = Kv.create();
                data.set("avatarUrl", avatarUrl);

                return RetKit.ok("头像修改成功！").set("data", data);
            } else {
                LogKit.error("用户头像数据库更新失败 - userId: " + user.getUserId());
                return RetKit.fail("头像修改失败！");
            }

        } catch (Exception e) {
            LogKit.error("修改用户头像异常", e);
            return RetKit.fail("头像修改失败！");
        }
    }

    /**
     * 获取用户信息
     *
     * @param accessToken 用户访问令牌
     * @return 用户信息
     */
    public RetKit getUserInfo(String accessToken) {
        try {
            // 验证用户会话
            User user = getUserByAccessToken(accessToken);
            if (user == null) {
                return RetKit.fail("未登录！");
            }

            String phone = user.getUserPhone();
            String email = user.getUserEmail();
            String avatarUrl = user.getUserImg();

            // 构建返回数据
            Kv data = Kv.create();
            data.set("userId", user.getUserId());
            data.set("userNickName", user.getUserNickName());
            data.set("avatarUrl", StrKit.notBlank(avatarUrl) ? avatarUrl : "");
            data.set("hasPhone", StrKit.notBlank(phone));
            data.set("hasEmail", StrKit.notBlank(email));

            // 返回完整的手机号和邮箱
            data.set("phone", StrKit.notBlank(phone) ? phone : "");
            data.set("email", StrKit.notBlank(email) ? email : "");

            return RetKit.ok("获取成功").set("data", data);

        } catch (Exception e) {
            LogKit.error("获取用户信息异常", e);
            return RetKit.fail("获取用户信息失败！");
        }
    }

    /**
     * 修改用户昵称
     *
     * @param accessToken 用户访问令牌
     * @param nickname    新昵称
     * @param ipAddress   IP地址
     * @return 修改结果
     */
    public RetKit updateNickname(String accessToken, String nickname, String ipAddress) {
        try {
            // 验证用户会话
            User user = getUserByAccessToken(accessToken);
            if (user == null) {
                return RetKit.fail("未登录！");
            }

            // 检查昵称是否为空
            if (StrKit.isBlank(nickname)) {
                return RetKit.fail("昵称不能为空！");
            }

            // 检查昵称是否与当前相同
            if (nickname.equals(user.getUserNickName())) {
                return RetKit.fail("新昵称与当前昵称相同！");
            }

            // 更新用户昵称
            boolean success = user.setUserNickName(nickname).update();
            if (success) {
                LogKit.info("用户昵称修改成功 - userId: " + user.getUserId() + ", newNickname: " + nickname);
                return RetKit.ok("昵称修改成功！");
            } else {
                return RetKit.fail("昵称修改失败！");
            }

        } catch (Exception e) {
            LogKit.error("修改用户昵称异常", e);
            return RetKit.fail("昵称修改失败！");
        }
    }

}