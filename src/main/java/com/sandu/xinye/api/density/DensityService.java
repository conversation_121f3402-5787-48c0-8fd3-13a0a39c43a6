package com.sandu.xinye.api.density;

import com.jfinal.kit.Kv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Density;
import com.sandu.xinye.common.model.MaterialGroup;
import com.sandu.xinye.common.model.Menu;
import com.sandu.xinye.common.model.User;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DensityService {
    public static final DensityService me = new DensityService();

    public RetKit getDensityList(String firmware) {
        String sql = "select * from density where firmware_version=?";
        List<Density> list = Density.dao.find(sql,firmware);
        return RetKit.ok("list", list);
    }
}
