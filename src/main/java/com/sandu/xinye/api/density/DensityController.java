package com.sandu.xinye.api.density;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Density;

@Clear
@Before({I18nInterceptor.class})
public class DensityController extends AdminController {

    @Clear
    public void index() {
        render("/admin_index.html");
    }


    public void getDensityList() {
        String firmware = getPara("firmware");
        RetKit ret = DensityService.me.getDensityList(firmware);
        renderJson(ret);
    }
}
