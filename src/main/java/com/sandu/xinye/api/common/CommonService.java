package com.sandu.xinye.api.common;

import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.j2cache.CacheKit;
import com.jfinal.i18n.I18n;
import com.jfinal.i18n.Res;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.*;
import com.sandu.xinye.common.model.User;
import java.text.MessageFormat;

public class CommonService {

	public static final CommonService me = new CommonService();

	public RetKit sendCaptcha(String phone, String type) {
		return sendCaptcha(phone, type, false);
	}

	public RetKit sendCaptcha(String phone, String type, Boolean isInternational) {
		if (StrKit.isBlank(phone) || type == null || StrKit.isBlank(type)) {
			return RetKit.fail("参数不能为空！");
		}
		User cu = User.dao.findFirst("select * from user where userPhone=?", phone);
		// 注销验证码：需要用户已注册且状态正常
		if (type.equals(Constant.SEND_CAPTCHA_TYPE_UNREGISTER)) {
			if (cu == null) {
				return RetKit.fail("该手机号码未注册！");
			}
			// 检查用户状态，已注销的用户不能再次注销
			if (cu.getStatus() != null && cu.getStatus() == User.STATUS_IS_DELETED) {
				return RetKit.fail("该账户已经被注销！");
			}
		}
		String isExistCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
		if (isExistCaptcha != null) {
			return RetKit.fail("1分钟内不能重复发送验证码！");
		}
		// 发送验证码
		String captcha = RandomKit.getRandomPsw(4);
		String result = "";
		if (isInternational) {
			result = new AliOpenapiKit().SendInternationalSMS(phone, captcha);
		} else {
			result = new AliOpenapiKit().SendSMS(phone, captcha);
		}
		if (result.equals("OK")) {
			CacheKit.put(CacheConstant.CAPTCHA, phone, captcha);
			return RetKit.ok();
		} else if (AliBusinessErr.hasError(result)) {
			CacheKit.remove(CacheConstant.CAPTCHA, phone);
			return RetKit.fail(AliBusinessErr.getErrorInfo(result));
		} else {
			CacheKit.remove(CacheConstant.CAPTCHA, phone);
			return RetKit.fail(result);
		}

	}

	public RetKit checkCaptcha(String phone, String captcha) {
		if (StrKit.isBlank(phone) || StrKit.isBlank(captcha)) {
			return RetKit.fail("手机号和验证码不能为空！");
		}
		String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
		if (StrKit.isBlank(realCaptcha)) {
			return RetKit.fail("验证码已失效，请重新发送验证码");
		}
		if (realCaptcha.equals(captcha)) {
			return RetKit.ok().setMsg("验证码正确！");
		} else {
			return RetKit.fail("验证失败！");
		}
	}

	/**
	 * 发送邮箱验证码
	 */
	public RetKit sendEmailCaptcha(String email, String type, String locale) {
		if (StrKit.isBlank(email) || type == null || StrKit.isBlank(type)) {
			return RetKit.fail("参数不能为空！");
		}
		if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$")) {
			return RetKit.fail("邮箱格式不正确！");
		}

		// 处理语言环境
		if (StrKit.isBlank(locale)) {
			locale = "zh_CN"; // 默认中文
		} else {
			locale = locale.split(",")[0]; // 取第一个语言
			// 映射常见的语言格式
			if ("en-US".equals(locale) || "en".equals(locale)) {
				locale = "en_US";
			} else if ("zh-CN".equals(locale) || "zh".equals(locale)) {
				locale = "zh_CN";
			}
		}

		User cu = User.dao.findFirst("select * from user where userEmail=?", email);
		if (type.equals(Constant.SEND_CAPTCHA_TYPE_UNREGISTER)) {
			if (cu == null) {
				return RetKit.fail("该邮箱未注册！");
			}
			// 检查用户状态，已注销的用户不能再次注销
			if (cu.getStatus() != null && cu.getStatus() == User.STATUS_IS_DELETED) {
				return RetKit.fail("该账户已经被注销！");
			}
		}
		String isExistCaptcha = CacheKit.get(CacheConstant.CAPTCHA, email);
		if (isExistCaptcha != null) {
			return RetKit.fail("1分钟内不能重复发送验证码！");
		}
		String captcha = RandomKit.getRandomPsw(4);

		// 使用多语言资源
		Res res = I18n.use("message", locale);
		String subjectKey = "email.subject.register";
		String contentKey = "email.content.register";

		if (type.equals(Constant.SEND_CAPTCHA_TYPE_UNREGISTER)) {
			subjectKey = "email.subject.unregister";
			contentKey = "email.content.unregister";
		} else if (type.equals(Constant.SEND_CAPTCHA_TYPE_LOGIN)) {
			subjectKey = "email.subject.login";
			contentKey = "email.content.login";
		}

		String subject = res.get(subjectKey);
		String content = MessageFormat.format(res.get(contentKey), captcha);

		// 如果多语言资源找不到，使用默认英文
		if (StrKit.isBlank(subject)) {
			subject = "Verification Code";
			if (type.equals(Constant.SEND_CAPTCHA_TYPE_UNREGISTER)) {
				subject = "Account Deactivation Verification Code";
			} else if (type.equals(Constant.SEND_CAPTCHA_TYPE_LOGIN)) {
				subject = "Login Verification Code";
			}
		}

		if (StrKit.isBlank(content)) {
			content = "Dear User: Hello, welcome to use Tattoo Go! Your registration verification code is: " + captcha
					+ " (valid for 1 minute)";
			if (type.equals(Constant.SEND_CAPTCHA_TYPE_UNREGISTER)) {
				content = "Dear User: Hello, you are deactivating your account. Your verification code is: " + captcha
						+ " (valid for 1 minute)";
			} else if (type.equals(Constant.SEND_CAPTCHA_TYPE_LOGIN)) {
				content = "Dear User: Hello, you are logging in. Your verification code is: " + captcha
						+ " (valid for 1 minute)";
			}
		}

		boolean result = EmailKit.sendMail(email, content, subject);
		if (result) {
			CacheKit.put(CacheConstant.CAPTCHA, email, captcha);
			return RetKit.ok();
		} else {
			CacheKit.remove(CacheConstant.CAPTCHA, email);
			return RetKit.fail("验证码发送失败，请稍后重试！");
		}
	}

	public RetKit checkEmailCaptcha(String email, String captcha) {
		if (StrKit.isBlank(email) || StrKit.isBlank(captcha)) {
			return RetKit.fail("邮箱和验证码不能为空！");
		}
		String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, email);
		if (StrKit.isBlank(realCaptcha)) {
			return RetKit.fail("验证码已失效，请重新发送验证码");
		}
		if (realCaptcha.equals(captcha)) {
			return RetKit.ok().setMsg("验证码正确！");
		} else {
			return RetKit.fail("验证失败！");
		}
	}

}
