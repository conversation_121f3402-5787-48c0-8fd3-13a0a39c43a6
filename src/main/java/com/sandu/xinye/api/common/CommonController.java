package com.sandu.xinye.api.common;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.kit.LogKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.admin.upload.UploadService;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.interceptor.RateLimitInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.annotation.RateLimit;

public class CommonController extends AppController {
	@Clear
	public void healthCheck() {
		renderNull();
	}

	@Clear
	@Before({ AttackAntiInterceptor.class, RateLimitInterceptor.class, I18nInterceptor.class })
	@RateLimit(2) // 验证码接口限制为每分钟2次
	public void sendCaptcha() {
		LogKit.info("调用发送验证码接口开始-----------------" + getParaToMap().toJson());
		String phone = getPara("phone");
		String type = getPara("type");
		Boolean isInternational = getParaToBoolean("intern", false);
		RetKit ret = CommonService.me.sendCaptcha(phone, type, isInternational);
		renderJson(ret);
		LogKit.info("调用发送验证码接口结束-----------------");
	}

	@Clear
	@Before({I18nInterceptor.class})
	public void checkCaptcha() {
		String phone = getPara("phone");
		String captcha = getPara("captcha");
		RetKit ret = CommonService.me.checkCaptcha(phone, captcha);
		renderJson(ret);
	}

	@Clear
	@Before({I18nInterceptor.class})
	public void checkEmailCaptcha() {
		String email = getPara("email");
		String captcha = getPara("captcha");
		RetKit ret = CommonService.me.checkEmailCaptcha(email, captcha);
		renderJson(ret);
	}

	@Clear
	@Before({ AttackAntiInterceptor.class, RateLimitInterceptor.class, I18nInterceptor.class })
	@RateLimit(2) // 邮箱验证码接口限制为每分钟2次
	public void sendEmailCaptcha() {
		LogKit.info("调用发送邮箱验证码接口开始-----------------" + getParaToMap().toJson());
		String email = getPara("email");
		String type = getPara("type");
		String locale = getHeader("Accept-Language");
		RetKit ret = CommonService.me.sendEmailCaptcha(email, type, locale);
		renderJson(ret);
		LogKit.info("调用发送邮箱验证码接口结束-----------------");
	}

}
