package com.sandu.xinye.api.app_version;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.GlobalInterceptor;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.AppVersion;

import java.io.File;

@Before({I18nInterceptor.class})
public class AppVersionController extends AppController {

    @Clear
    public void getNewestVersion() {
        RetKit ret = AppVersionService.me.getNewestVersion();
        renderJson(ret);
    }

    @Clear
    @Before(GlobalInterceptor.class)
    public void downloadApk() {
        String url = AppVersionService.me.downloadApkUrl();
        renderJson(RetKit.ok("url", url));
    }
}
