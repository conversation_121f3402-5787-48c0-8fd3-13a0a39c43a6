# OSS配置管理说明

## 概述

批量上传功能现在支持通过配置文件动态调整上传限制，无需修改代码即可调整上传策略。

## 配置文件位置

**主配置文件**: `src/main/resources/common_config.txt`

## 配置项说明

### 批量上传配置

```properties
##批量上传配置
# 批量上传图片最大数量
oss.batch.upload.maxCount=10
# 单个图片文件最大大小（MB）
oss.single.upload.maxSizeMB=10
```

### 配置项详解

| 配置项 | 说明 | 默认值 | 取值范围 |
|--------|------|--------|----------|
| `oss.batch.upload.maxCount` | 批量上传最大文件数量 | 10 | 1-50（建议） |
| `oss.single.upload.maxSizeMB` | 单个文件最大大小（MB） | 10 | 1-100（建议） |

## 配置管理类

### OssConfig.java

位置：`src/main/java/com/sandu/xinye/common/config/OssConfig.java`

**主要方法**：

```java
// 获取批量上传最大数量
public static int getBatchUploadMaxCount()

// 获取单个文件最大大小（MB）
public static int getSingleUploadMaxSizeMB()

// 获取单个文件最大大小（字节）
public static long getSingleUploadMaxSizeBytes()

// 验证批量上传数量是否超限
public static boolean isBatchCountExceeded(int count)

// 验证单个文件大小是否超限
public static boolean isSingleFileSizeExceeded(long fileSizeBytes)

// 获取配置信息描述
public static String getBatchUploadConfigInfo()
```

## 配置生效方式

### 1. 重启应用
修改配置文件后，重启应用即可生效。

### 2. 热更新（如果支持）
如果系统支持配置热更新，可以通过管理接口重新加载配置。

## 配置获取接口

### 接口信息
- **URL**: `/api/oss/getUploadConfig`
- **方法**: GET
- **权限**: 无需登录

### 响应示例
```json
{
  "state": "ok",
  "maxFileCount": 10,
  "maxFileSizeMB": 10,
  "maxFileSizeBytes": 10485760,
  "supportedFormats": ["jpg", "jpeg", "png", "bmp"],
  "configInfo": "批量上传最多支持10张图片，单张图片最大10MB"
}
```

## 配置建议

### 生产环境建议

```properties
# 生产环境推荐配置
oss.batch.upload.maxCount=5
oss.single.upload.maxSizeMB=5
```

**理由**：
- 减少服务器负载
- 提高上传成功率
- 降低网络传输风险

### 开发环境建议

```properties
# 开发环境推荐配置
oss.batch.upload.maxCount=10
oss.single.upload.maxSizeMB=10
```

**理由**：
- 方便测试各种场景
- 模拟用户真实使用情况

### 高性能环境建议

```properties
# 高性能环境推荐配置
oss.batch.upload.maxCount=20
oss.single.upload.maxSizeMB=20
```

**理由**：
- 充分利用服务器性能
- 提高用户体验

## 监控建议

### 1. 配置使用情况监控
- 监控实际上传文件数量分布
- 监控文件大小分布
- 根据使用情况调整配置

### 2. 性能监控
- 监控批量上传接口响应时间
- 监控OSS上传成功率
- 监控服务器资源使用情况

### 3. 错误监控
- 监控因配置限制导致的上传失败
- 监控配置加载异常
- 设置合适的告警阈值

## 故障排查

### 1. 配置不生效
**检查项**：
- 配置文件路径是否正确
- 配置项名称是否正确
- 是否重启了应用
- 日志中是否有配置加载错误

### 2. 上传限制异常
**检查项**：
- 配置值是否在合理范围内
- 是否有特殊字符或格式错误
- 默认值是否正确

### 3. 前端显示配置错误
**检查项**：
- 配置获取接口是否正常
- 前端是否正确调用配置接口
- 网络是否正常

## 扩展说明

### 1. 添加新配置项
如需添加新的配置项，请：
1. 在 `common_config.txt` 中添加配置项
2. 在 `OssConfig.java` 中添加对应的获取方法
3. 更新相关业务逻辑
4. 更新文档

### 2. 配置验证
建议在 `OssConfig.java` 中添加配置验证逻辑：
```java
public static boolean validateConfig() {
    int maxCount = getBatchUploadMaxCount();
    int maxSize = getSingleUploadMaxSizeMB();
    
    return maxCount > 0 && maxCount <= 50 && 
           maxSize > 0 && maxSize <= 100;
}
```

### 3. 配置缓存
如果配置读取频繁，可以考虑添加缓存机制：
```java
private static volatile Integer cachedMaxCount = null;
private static long lastLoadTime = 0;
private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟

public static int getBatchUploadMaxCount() {
    long now = System.currentTimeMillis();
    if (cachedMaxCount == null || (now - lastLoadTime) > CACHE_EXPIRE_TIME) {
        cachedMaxCount = PropKit.getInt("oss.batch.upload.maxCount", 10);
        lastLoadTime = now;
    }
    return cachedMaxCount;
}
```

## 总结

通过配置化管理，批量上传功能现在具备了更好的灵活性和可维护性：

1. **灵活性**：可根据实际需求调整限制
2. **可维护性**：无需修改代码即可调整策略
3. **可监控性**：提供配置获取接口便于监控
4. **可扩展性**：易于添加新的配置项

建议定期根据实际使用情况和系统性能调整配置，以达到最佳的用户体验和系统性能平衡。
