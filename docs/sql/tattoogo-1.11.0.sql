-- user表增加邮箱字段
ALTER TABLE `user` ADD COLUMN `userEmail` varchar(100) DEFAULT NULL COMMENT '邮箱' AFTER `userPhone`;

-- 为相关业务表添加软删除字段（如果不存在的话）
-- templet表添加软删除字段
ALTER TABLE `templet` ADD COLUMN `isDel` tinyint(1) DEFAULT '0' COMMENT '是否已删除(0:否,1:是)' AFTER `updateTime`;
ALTER TABLE `templet` ADD COLUMN `deleteTime` datetime DEFAULT NULL COMMENT '删除时间' AFTER `isDel`;

-- templet_group表添加软删除字段  
ALTER TABLE `templet_group` ADD COLUMN `isDel` tinyint(1) DEFAULT '0' COMMENT '是否已删除(0:否,1:是)' AFTER `updateTime`;
ALTER TABLE `templet_group` ADD COLUMN `deleteTime` datetime DEFAULT NULL COMMENT '删除时间' AFTER `isDel`;

-- feedback表添加软删除字段
ALTER TABLE `feedback` ADD COLUMN `isDel` tinyint(1) DEFAULT '0' COMMENT '是否已删除(0:否,1:是)' AFTER `createTime`;
ALTER TABLE `feedback` ADD COLUMN `deleteTime` datetime DEFAULT NULL COMMENT '删除时间' AFTER `isDel`;

-- 为软删除字段添加索引以提升查询性能
CREATE INDEX `idx_isDel_deleteTime` ON `templet` (`isDel`, `deleteTime`);
CREATE INDEX `idx_isDel_deleteTime` ON `templet_group` (`isDel`, `deleteTime`);
CREATE INDEX `idx_isDel_deleteTime` ON `feedback` (`isDel`, `deleteTime`);

-- 用户数据备份表
CREATE TABLE `user_backup` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `userId` int(11) NOT NULL COMMENT '用户ID',
  `userData` longtext NOT NULL COMMENT '用户完整数据(JSON格式)',
  `reason` varchar(500) DEFAULT NULL COMMENT '注销原因',
  `ipAddress` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `backupTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '备份时间',
  `expireTime` datetime NOT NULL COMMENT '过期时间(30天后)',
  `isDeleted` tinyint(1) DEFAULT '0' COMMENT '是否已删除(0:否,1:是)',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userId` (`userId`),
  KEY `idx_backupTime` (`backupTime`),
  KEY `idx_expireTime` (`expireTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户数据备份表';

-- 用户注销操作日志表
CREATE TABLE `user_unregister_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `userId` int(11) NOT NULL COMMENT '用户ID',
  `reason` varchar(500) DEFAULT NULL COMMENT '注销原因',
  `ipAddress` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `status` varchar(100) NOT NULL COMMENT '操作状态(SUCCESS/FAILED等)',
  `operateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_userId` (`userId`),
  KEY `idx_operateTime` (`operateTime`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户注销操作日志表';

-- 添加表注释和字段说明
ALTER TABLE `user_backup` COMMENT = '用户数据备份表 - 用于存储用户注销时的数据备份，保留30天便于数据恢复';
ALTER TABLE `user_unregister_log` COMMENT = '用户注销日志表 - 记录所有用户注销操作的详细日志';

-- 创建定时清理过期备份数据的存储过程（简化版）
DROP PROCEDURE IF EXISTS `CleanExpiredUserBackup`;
DELIMITER $$
CREATE PROCEDURE `CleanExpiredUserBackup`()
BEGIN
    DECLARE backupCleanupCount INT DEFAULT 0;
    DECLARE relatedDataCleanupCount INT DEFAULT 0;
    
    -- 第一阶段：软删除过期的备份数据（标记为已删除）
    UPDATE `user_backup` 
    SET `isDeleted` = 1, `updateTime` = NOW()
    WHERE `expireTime` < NOW() AND `isDeleted` = 0;
    
    -- 获取软删除的备份记录数量
    SELECT ROW_COUNT() INTO backupCleanupCount;
    
    -- 第二阶段：物理删除已经软删除超过7天的备份数据
    DELETE FROM `user_backup` 
    WHERE `isDeleted` = 1 AND `updateTime` < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- 第三阶段：清理相关表中软删除超过30天的数据
    DELETE FROM `templet` 
    WHERE `isDel` = 1 AND `deleteTime` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    DELETE FROM `templet_group` 
    WHERE `isDel` = 1 AND `deleteTime` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    DELETE FROM `feedback` 
    WHERE `isDel` = 1 AND `deleteTime` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 获取清理的相关数据数量
    SELECT ROW_COUNT() INTO relatedDataCleanupCount;
    
    -- 记录清理操作日志
    INSERT INTO `user_unregister_log` (`userId`, `reason`, `status`, `operateTime`)
    VALUES (0, CONCAT('System auto cleanup - backup records: ', backupCleanupCount, ', related data: ', relatedDataCleanupCount), 
            'CLEANUP_SUCCESS', NOW());
            
END$$
DELIMITER ;

-- 创建定时任务
DROP EVENT IF EXISTS `clean_expired_backup`;
CREATE EVENT `clean_expired_backup`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 1 DAY
DO CALL CleanExpiredUserBackup(); 