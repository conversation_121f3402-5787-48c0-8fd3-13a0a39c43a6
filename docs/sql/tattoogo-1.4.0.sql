use xprick_test;

# 素材类别多语言
alter table material_group add column nameDe  varchar(32) DEFAULT NULL COMMENT '德语';
alter table material_group add column nameFr  varchar(32) DEFAULT NULL COMMENT '法语';
alter table material_group add column nameIt  varchar(32) DEFAULT NULL COMMENT '意大利语';
alter table material_group add column namePt  varchar(32) DEFAULT NULL COMMENT '葡萄牙语';

# 去掉冗余表
drop table  if exists  `survey`;
drop table  if exists  `survey_answer`;
drop table  if exists  `survey_question`;

# 字体管理
drop table if exists `font`;
CREATE TABLE `font` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `fontName` varchar(32) NOT NULL COMMENT '字体显示名称',
  `fontLocale` varchar(32) DEFAULT NULL COMMENT '字体所属语言',
  `fontUrl` varchar(128) NOT NULL COMMENT '字体路径',
  `fontValue` varchar(50) DEFAULT NULL COMMENT '字体唯一标识',
  `fontCover` varchar(500) NOT NULL COMMENT '封面图片地址',
  `sort` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='字体表';
# 字体多语言
drop table if exists `font_kind_lang`;
CREATE TABLE `font_locale` (
                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                               `locale` varchar(32) NOT NULL COMMENT '语言类型 zh-cn',
                               `name` varchar(32) DEFAULT NULL COMMENT '简称',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `idx_locale` (`locale`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='字体类型语言表';
