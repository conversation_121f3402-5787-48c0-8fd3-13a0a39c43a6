<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量上传图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        input[type="file"] {
            padding: 8px;
        }
        
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .progress {
            margin-top: 10px;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            display: none;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .image-preview {
            margin-top: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .preview-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            background: white;
        }
        
        .preview-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .preview-item .info {
            padding: 8px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>批量上传图片测试</h1>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="token">访问令牌 (X-Access-Token):</label>
                <input type="text" id="token" placeholder="请输入您的访问令牌" required>
            </div>
            
            <div class="form-group">
                <label for="fileInput">选择图片文件 (最多10张):</label>
                <input type="file" id="fileInput" name="file" multiple accept="image/*" required>
                <div class="file-info">
                    支持格式: JPG, JPEG, PNG, BMP<br>
                    单文件大小: 最大10MB<br>
                    批量数量: 最多10张
                </div>
            </div>
            
            <div class="buttons">
                <button type="button" class="btn-secondary" onclick="clearAll()">清空</button>
                <button type="button" class="btn-success" onclick="uploadSingle()">单图上传</button>
                <button type="button" class="btn-primary" onclick="uploadBatch()">批量上传（宽松模式）</button>
                <button type="button" class="btn-primary" onclick="uploadBatchStrict()">批量上传（严格模式）</button>
            </div>

            <div style="margin-top: 10px; font-size: 14px; color: #666;">
                <strong>模式说明：</strong><br>
                • <strong>宽松模式</strong>：超过10张时，只上传前10张，其余跳过<br>
                • <strong>严格模式</strong>：超过10张时，直接拒绝所有文件
            </div>
        </form>
        
        <div class="progress" id="progress">
            <div class="progress-bar" id="progressBar">0%</div>
        </div>
        
        <div id="result"></div>
        
        <div id="imagePreview" class="image-preview"></div>
    </div>

    <script>
        const API_BASE = '/api/oss';
        
        // 文件选择事件
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = e.target.files;
            showFilePreview(files);
            
            if (files.length > 10) {
                showResult('警告: 最多只能选择10张图片，当前选择了 ' + files.length + ' 张', 'error');
            }
        });
        
        // 表单提交事件（批量上传）
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            uploadBatch();
        });
        
        // 显示文件预览
        function showFilePreview(files) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = '';
            
            Array.from(files).forEach((file, index) => {
                if (index >= 10) return; // 只显示前10个
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    const div = document.createElement('div');
                    div.className = 'preview-item';
                    div.innerHTML = `
                        <img src="${e.target.result}" alt="预览">
                        <div class="info">
                            ${file.name}<br>
                            ${(file.size / 1024 / 1024).toFixed(2)} MB
                        </div>
                    `;
                    preview.appendChild(div);
                };
                reader.readAsDataURL(file);
            });
        }
        
        // 批量上传
        async function uploadBatch() {
            const token = document.getElementById('token').value.trim();
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            
            if (!token) {
                showResult('请输入访问令牌', 'error');
                return;
            }
            
            if (files.length === 0) {
                showResult('请选择图片文件', 'error');
                return;
            }
            
            if (files.length > 10) {
                showResult('最多只能选择10张图片', 'error');
                return;
            }
            
            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('file', files[i]);
            }
            
            showProgress(true);
            
            try {
                const response = await fetch(API_BASE + '/uploadImgBatch', {
                    method: 'POST',
                    headers: {
                        'X-Access-Token': token
                    },
                    body: formData
                });
                
                const result = await response.json();
                showProgress(false);
                
                if (result.state === 'ok') {
                    let message = `批量上传完成（宽松模式）!\n\n成功上传 ${result.data.length} 张图片:\n`;
                    result.data.forEach((url, index) => {
                        message += `${index + 1}. ${url}\n`;
                    });

                    if (result.warning) {
                        message += `\n⚠️ 警告: ${result.warning}\n`;
                    }

                    if (result.failedFiles && result.failedFiles.length > 0) {
                        message += `\n失败文件 ${result.failedFiles.length} 个:\n`;
                        result.failedFiles.forEach((error, index) => {
                            message += `${index + 1}. ${error}\n`;
                        });
                    }

                    showResult(message, 'success');
                } else {
                    let message = `上传失败: ${result.msg}`;
                    if (result.failedFiles) {
                        message += '\n\n失败详情:\n';
                        result.failedFiles.forEach((error, index) => {
                            message += `${index + 1}. ${error}\n`;
                        });
                    }
                    showResult(message, 'error');
                }
            } catch (error) {
                showProgress(false);
                showResult('请求失败: ' + error.message, 'error');
            }
        }

        // 批量上传（严格模式）
        async function uploadBatchStrict() {
            const token = document.getElementById('token').value.trim();
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;

            if (!token) {
                showResult('请输入访问令牌', 'error');
                return;
            }

            if (files.length === 0) {
                showResult('请选择图片文件', 'error');
                return;
            }

            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('file', files[i]);
            }

            showProgress(true);

            try {
                const response = await fetch(API_BASE + '/uploadImgBatchStrict', {
                    method: 'POST',
                    headers: {
                        'X-Access-Token': token
                    },
                    body: formData
                });

                const result = await response.json();
                showProgress(false);

                if (result.state === 'ok') {
                    let message = `批量上传完成（严格模式）!\n\n成功上传 ${result.data.length} 张图片:\n`;
                    result.data.forEach((url, index) => {
                        message += `${index + 1}. ${url}\n`;
                    });

                    if (result.warning) {
                        message += `\n⚠️ 警告: ${result.warning}\n`;
                    }

                    if (result.failedFiles && result.failedFiles.length > 0) {
                        message += `\n失败文件 ${result.failedFiles.length} 个:\n`;
                        result.failedFiles.forEach((error, index) => {
                            message += `${index + 1}. ${error}\n`;
                        });
                    }

                    showResult(message, 'success');
                } else {
                    let message = `上传失败（严格模式）: ${result.msg}`;
                    if (result.failedFiles) {
                        message += '\n\n失败详情:\n';
                        result.failedFiles.forEach((error, index) => {
                            message += `${index + 1}. ${error}\n`;
                        });
                    }
                    showResult(message, 'error');
                }
            } catch (error) {
                showProgress(false);
                showResult('请求失败: ' + error.message, 'error');
            }
        }

        // 单图上传
        async function uploadSingle() {
            const token = document.getElementById('token').value.trim();
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            
            if (!token) {
                showResult('请输入访问令牌', 'error');
                return;
            }
            
            if (files.length === 0) {
                showResult('请选择图片文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', files[0]); // 只上传第一个文件
            
            showProgress(true);
            
            try {
                const response = await fetch(API_BASE + '/uploadImg', {
                    method: 'POST',
                    headers: {
                        'X-Access-Token': token
                    },
                    body: formData
                });
                
                const result = await response.json();
                showProgress(false);
                
                if (result.state === 'ok') {
                    showResult(`单图上传成功!\n\n图片地址: ${result.data}`, 'success');
                } else {
                    showResult(`单图上传失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                showProgress(false);
                showResult('请求失败: ' + error.message, 'error');
            }
        }
        
        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
        }
        
        // 显示/隐藏进度条
        function showProgress(show) {
            const progress = document.getElementById('progress');
            if (show) {
                progress.style.display = 'block';
                // 模拟进度
                let width = 0;
                const interval = setInterval(() => {
                    width += 10;
                    document.getElementById('progressBar').style.width = width + '%';
                    document.getElementById('progressBar').textContent = width + '%';
                    if (width >= 90) {
                        clearInterval(interval);
                    }
                }, 100);
            } else {
                progress.style.display = 'none';
                document.getElementById('progressBar').style.width = '0%';
            }
        }
        
        // 清空所有
        function clearAll() {
            document.getElementById('uploadForm').reset();
            document.getElementById('result').innerHTML = '';
            document.getElementById('imagePreview').innerHTML = '';
            showProgress(false);
        }
    </script>
</body>
</html>
